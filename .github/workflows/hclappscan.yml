# HCLAppScan (SAST & SCA) will be enabled if the request is received from <PERSON><PERSON><PERSON> ( Cyber Security Team  HCL AppCloud Admin)
# This is a reusable GitHub Action that can be used to run HCL AppScan (SAST & SCA) on a given application/repository.
name: <PERSON>L App Scan 
on: 
  workflow_dispatch: # Allows the scan to be run manually
  schedule:
    - cron: '0 15 * * 6' # every Saturday at 10:00AM
jobs:
  RunHCLAppScan:
    uses: "AC-IT-Development/Dbaas-Reusable-GitHub-Actions/.github/workflows/HCL-AppScan.yml@main"
    with:
      application_id: ${{ vars.HCL_APPLICATION_ID }}
    secrets:
      asoc_key: ${{ secrets.HCL_APP_SCAN_API_KEY_ID }}
      asoc_secret: ${{ secrets.HCL_APP_SCAN_API_KEY_SECRET }}
