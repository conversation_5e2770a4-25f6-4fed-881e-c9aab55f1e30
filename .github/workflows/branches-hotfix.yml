name: branches-hotfix
permissions:
  actions: write
  contents: read
  id-token: write
  packages: write
  pull-requests: write
  repository-projects: write

on:
  push:
    branches: hotfix/*
jobs:
  step_job_1:
    runs-on: ubuntu-latest
    container:
      image: atlassian/default-image:4
    steps:
    - uses: actions/checkout@v4.1.0
    - name: Set up SSH
      run: |
         home_dir=$(eval echo ~$(whoami))
         mkdir -p $home_dir/.ssh
         echo "$SSH_PRIVATE_KEY" > $home_dir/.ssh/${{ vars.SA_GH_AUTH_SSH_PRIVATE_KEY_FILE_NAME  }}
         chmod 400 $home_dir/.ssh/${{ vars.SA_GH_AUTH_SSH_PRIVATE_KEY_FILE_NAME  }}
         echo ${{ vars.GH_HOST_FINGERPRINT }} >> $home_dir/.ssh/known_hosts
      env:
         SSH_PRIVATE_KEY: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}
    - name: Build and Test
      run: echo "start"
  step_job_2:
    runs-on: ubuntu-latest
    environment:
      name: CRTCA1
    container:
      image: atlassian/default-image:4
    needs:
    - step_job_1
    steps:
    - uses: actions/checkout@v4.1.0
    - name: Set up SSH
      run: |
         home_dir=$(eval echo ~$(whoami))
         mkdir -p $home_dir/.ssh
         echo "$SSH_PRIVATE_KEY" > $home_dir/.ssh/${{ vars.SA_GH_AUTH_SSH_PRIVATE_KEY_FILE_NAME  }}
         chmod 400 $home_dir/.ssh/${{ vars.SA_GH_AUTH_SSH_PRIVATE_KEY_FILE_NAME  }}
         echo ${{ vars.GH_HOST_FINGERPRINT }} >> $home_dir/.ssh/known_hosts
      env:
         SSH_PRIVATE_KEY: ${{ secrets.SERVICE_AC_PVT_SSH_KEY }}
    - name: Install Git
      run: |
        apt-get update
        apt-get install -y git
    - name: Checkout Pre-Requisite
      uses: actions/checkout@v4
      with:
        repository: AC-IT-Development/m3-pipeline-library.git
        ref: develop
        token: ${{ secrets.SERVICEACCOUNT_PAT_TOKEN }}
        path: m3-pipeline-library
    - name: Set AWS Session Name
      id: repo_name
      run: echo "::set-output name=repo_name::$(echo ${{ github.repository }} | cut -d '/' -f 2)"
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-region: ${{ vars.AWS_DEFAULT_REGION }}
        role-to-assume: ${{ vars.AWS_DBAAS_CRTCA_OIDC_ROLE_ARN }}
        role-session-name: '${{ steps.repo_name.outputs.REPO_NAME }}-SLS-Deployment'
    - name: Deploy to CRTCA1 ca-central-1
      run: |-
        apt-get update
        apt-get install -y python3 python3-pip bash jq git openssh-client libpq-dev g++ make wget
        wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq
        chmod +x /usr/bin/yq
        yq --version
        pip install awscli
        chmod +x dynatrace_script.sh
        ./dynatrace_script.sh crtca1 us-east-2  /crt/dbaas/dynatrace/oneagent_credentials
        aws --version
        npm install -g serverless@3
        originalPath=$PWD
        cd stacks/aflvfl-glue-catalog
        npm install
        sls deploy --stage crtca1 --region ca-central-1 --verbose
        cd $originalPath
        originalPath=$PWD
        cd stacks/aflvfl-ingestion-dlq-alarm
        npm install
        sls deploy --stage crtca1 --region ca-central-1 --verbose
        cd $originalPath
        originalPath=$PWD
        cd stacks/aflvfl-ingestion
        npm install
        sls deploy --stage crtca1 --region ca-central-1 --verbose
        cd $originalPath
        echo "deployed to CRTCA1"