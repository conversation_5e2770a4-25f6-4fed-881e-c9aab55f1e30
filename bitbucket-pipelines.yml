image: node:16-alpine
definitions:
  caches:
    sonar-cache: .sonar

steps:
  - step: &utest
      name: Run Unit Test
      caches:
        - node
      script:
        - cd stacks/aflvfl-ingestion/
        - apk update
        - apk add --no-cache python3 py-pip bash jq git openssh-client libpq-dev g++ make
        - npm install -g jest
        # - npm install
        - rm -rf ~/.node-gyp && rm -rf package-lock.json && npm install --unsafe-perm
        - npm run test
      artifacts:
        - stacks/aflvfl-ingestion/coverage/**

  - step: &sonar
      name: Sonar Code Analysis
      caches:
        - sonar-cache
      image: sonarsource/sonar-scanner-cli:4.7.0
      script:
        - export SONAR_LOGIN=$SONAR_API_TOKEN
        - export SONAR_PROJECT_BASE_DIR=.
        - /opt/sonar-scanner/bin/sonar-scanner -Dsonar.login=$SONAR_API_TOKEN -Dsonar.projectKey=argo:$BITBUCKET_REPO_SLUG -Dsonar.projectName=$BITBUCKET_REPO_SLUG -Dsonar.projectVersion=$BITBUCKET_BUILD_NUMBER -Dsonar.host.url=$SONAR_HOST_URL

pipelines:
  custom: # Pipelines that can only be scheduled.
    HCLAppScan:
      - step:
          name: HCLApp Scan
          image: atlassian/default-image:4
          script:
            # Custom Pipe to run Static Analysis via HCL AppScan on Cloud
              - apt update
              - apt install -y curl unzip maven openjdk-11-jre gradle && apt clean
              - cd .. && export WORKING_DIR=$(pwd)
              - curl https://cloud.appscan.com/api/v4/Tools/SAClientUtil?os=linux > SAClientUtil.zip
              - unzip SAClientUtil.zip
              - rm -f SAClientUtil.zip
              - mv SAClientUtil.* SAClientUtil
              - export PATH="${WORKING_DIR}/SAClientUtil/bin:${PATH}"
              # Generate IRX files based on source root folder downloaded by Bitbucket
              - cd "${BITBUCKET_CLONE_DIR}"
              - appscan.sh prepare -n "${BITBUCKET_REPO_FULL_NAME//\//_}-${BITBUCKET_BUILD_NUMBER}" -d .
              # Authenticate in ASoC
              - appscan.sh api_login -u $APPSCAN_API_KEY_ID -P $APPSCAN_API_KEY_SECRET -persist
              # Upload IRX file to ASoC to be analyzed and receive scanId
              - appscan.sh queue_analysis -a $APPSCAN_APP_ID
  branches:
    main:
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to PRODCA1 ca-central-1
          deployment: PRODCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client libpq-dev g++ make
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/aflvfl-glue-catalog
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/aflvfl-ingestion-dlq-alarm
            - bash ./pipeline-library/deployScripts/deploy_digital.bash prodca1 ca-central-1 stacks/aflvfl-ingestion
            - echo "deployed to PRODCA1"

    "hotfix/*":
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to CRTCA1 ca-central-1
          deployment: CRTCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client libpq-dev g++ make
            - pip install awscli
            - aws --version
            - npm install -g serverless
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/aflvfl-glue-catalog
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/aflvfl-ingestion-dlq-alarm
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/aflvfl-ingestion
            - echo "deployed to CRTCA1"

    release:
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to CRTCA1 ca-central-1
          deployment: CRTCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client libpq-dev g++ make
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/aflvfl-glue-catalog
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/aflvfl-ingestion-dlq-alarm
            - bash ./pipeline-library/deployScripts/deploy_digital.bash crtca1 ca-central-1 stacks/aflvfl-ingestion
            - echo "deployed to CRTCA1"
      - step:
          name: Deploy to PREPRODCA1 ca-central-1
          deployment: PREPRODCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client
            - pip install awscli
            - aws --version
            - npm install -g serverless@2.72.2
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/aflvfl-glue-catalog
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/aflvfl-ingestion-dlq-alarm
            - bash ./pipeline-library/deployScripts/deploy_digital.bash preprodca1 ca-central-1 stacks/aflvfl-ingestion
            - echo "deployed to PREPRODCA1"

    bat:
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to BATCA1 ca-central-1
          deployment: BATCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client libpq-dev g++ make
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/aflvfl-glue-catalog
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/aflvfl-ingestion-dlq-alarm
            - bash ./pipeline-library/deployScripts/deploy_digital.bash batca1 ca-central-1 stacks/aflvfl-ingestion
            - echo "deployed to BATCA1"

    dev:
      - step: *utest
      - step: *sonar
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to INTCA1 ca-central-1
          deployment: INTCA1
          trigger: manual
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client libpq-dev g++ make
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/aflvfl-glue-catalog
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/aflvfl-ingestion-dlq-alarm
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/aflvfl-ingestion
            - echo "deployed to INTCA1"

    feature/*:
      - step: *utest
      - step: *sonar
      - step:
          name: Build and Test
          script:
            - echo "start"
      - step:
          name: Deploy to INTCA1 ca-central-1
          deployment: INTCA1
          script:
            - apk update
            - apk add --no-cache python3 py-pip bash jq git openssh-client libpq-dev g++ make
            - pip install awscli
            - aws --version
            - npm install -g serverless@3.38.0
            - <NAME_EMAIL>:aircanada-m3/pipeline-library.git
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/aflvfl-glue-catalog
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/aflvfl-ingestion-dlq-alarm
            - bash ./pipeline-library/deployScripts/deploy_digital.bash intca1 ca-central-1 stacks/aflvfl-ingestion
            - echo "deployed to INTCA1"
