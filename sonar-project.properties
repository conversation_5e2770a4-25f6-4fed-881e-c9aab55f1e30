sonar.sources=stacks
sonar.exclusions=stacks/**/webpack.config.js,stacks/**/*constants.js,stacks/**/flight-event-types.js
# Where to find tests file, also src
sonar.tests=stacks
# But we get specific here
# We don't need to exclude it in sonar.sources because it is automatically taken care of
sonar.test.inclusions=stacks/**/*.spec.js,stacks/**/*.spec.jsx,stacks/**/*.test.js,stacks/**/*.test.jsx,tests/**/*.spec.js,tests/**/*.spec.jsx,tests/**/*.test.js,tests/**/*.test.jsx
sonar.test.exclusions= stacks/**/sonar-project.js,stacks/**/.eslintrc.js
sonar.coverage.exclusions= sonar-project.js,stacks/**/.eslintrc.js
# Now specify path of lcov and testlog
sonar.javascript.lcov.reportPaths=stacks/aflvfl-ingestion/coverage/lcov.info
sonar.testExecutionReportPaths=stacks/aflvfl-ingestion/coverage/reporter.xml