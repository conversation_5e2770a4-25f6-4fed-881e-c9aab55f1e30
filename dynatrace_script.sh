#!/bin/bash

set -e

STAGE=${1:-intca1}
REGION=${2:-us-east-2}
SECRETE_NAME=${3:-/int/dbaas/dynatrace/oneagent_credentials}

echo "🔄 Fetching secrets for stage: $STAGE"

# Fetch the secret from AWS Secrets Manager
SECRET_JSON=$(aws secretsmanager get-secret-value \
  --secret-id "$SECRETE_NAME" \
  --region "$REGION" \
  --query SecretString \
  --output text)


DT_TENANT=$(echo "$SECRET_JSON" | jq -r '.DT_TENANT // ""')
yq -i '.custom.params.'"$STAGE"'.DT_TENANT = "'"$DT_TENANT"'"' defaults.yml

AWS_LAMBDA_EXEC_WRAPPER=$(echo "$SECRET_JSON" | jq -r '.AWS_LAMBDA_EXEC_WRAPPER // ""')
yq -i '.custom.params.'"$STAGE"'.AWS_LAMBDA_EXEC_WRAPPER = "'"$AWS_LAMBDA_EXEC_WRAPPER"'"' defaults.yml

# DT_CLUSTER_ID=$(echo "$SECRET_JSON" | jq -r '.DT_CLUSTER_ID // ""')
# yq -i '.custom.params.'"$STAGE"'.DT_CLUSTER_ID = "'"$DT_CLUSTER_ID"'"' defaults.yml

DT_CLUSTER_ID=$(echo "$SECRET_JSON" | jq -r '.DT_CLUSTER_ID // empty')
yq -i ".custom.params.\"$STAGE\".DT_CLUSTER_ID = $DT_CLUSTER_ID" defaults.yml

DT_CONNECTION_BASE_URL=$(echo "$SECRET_JSON" | jq -r '.DT_CONNECTION_BASE_URL // ""')
yq -i '.custom.params.'"$STAGE"'.DT_CONNECTION_BASE_URL = "'"$DT_CONNECTION_BASE_URL"'"' defaults.yml

DT_CONNECTION_AUTH_TOKEN=$(echo "$SECRET_JSON" | jq -r '.DT_CONNECTION_AUTH_TOKEN // ""')
yq -i '.custom.params.'"$STAGE"'.DT_CONNECTION_AUTH_TOKEN = "'"$DT_CONNECTION_AUTH_TOKEN"'"' defaults.yml

# DT_OPEN_TELEMETRY_ENABLE_INTEGRATION=$(echo "$SECRET_JSON" | jq -r '.DT_OPEN_TELEMETRY_ENABLE_INTEGRATION // ""')
# yq -i '.custom.params.'"$STAGE"'.DT_OPEN_TELEMETRY_ENABLE_INTEGRATION = "'"$DT_OPEN_TELEMETRY_ENABLE_INTEGRATION"'"' defaults.yml


cat defaults.yml

