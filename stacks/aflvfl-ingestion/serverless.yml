service: ${file(../../defaults.yml):service}-writer

variablesResolutionMode: 20210326

provider:
  name: aws
  stage: ${opt:stage, "intca1"}
  architecture: arm64
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}
  runtime: nodejs16.x
  region: ${file(../../defaults.yml):provider.region}
  stackTags: ${self:custom.tags}
  tracing:    
    lambda: true
  environment:
    AWS_LAMBDA_EXEC_WRAPPER: ${self:custom.defaultParams.${self:provider.stage}.AWS_LAMBDA_EXEC_WRAPPER}
    DT_TENANT: ${self:custom.defaultParams.${self:provider.stage}.DT_TENANT}
    DT_CLUSTER_ID: ${self:custom.defaultParams.${self:provider.stage}.DT_CLUSTER_ID}
    DT_CONNECTION_BASE_URL: ${self:custom.defaultParams.${self:provider.stage}.DT_CONNECTION_BASE_URL}
    DT_CONNECTION_AUTH_TOKEN: ${self:custom.defaultParams.${self:provider.stage}.DT_CONNECTION_AUTH_TOKEN}
    KAFKA_TOPIC: ${self:custom.defaultParams.${self:provider.stage}.DT_CONNECTION_AUTH_TOKEN}
    SECRET_DIGITAL_ODS_CREDENTIALS_DB: ${self:custom.defaultParams.${self:provider.stage}.SECRET_DIGITAL_ODS_CREDENTIALS_DB} 
    DATABASE_NAME: ${self:custom.defaultParams.${self:provider.stage}.DATABASE_NAME} 

plugins:
  - serverless-offline
  - serverless-prune-plugin
  - serverless-plugin-log-retention
  # - serverless-webpack
  - serverless-esbuild
  
package:
  individually: true

custom:
  defaults: ${file(../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  defaultParams: ${self:custom.defaults.custom.params}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}
  layerStackName: dbaas-sre-infra-v3-layer-${self:provider.stage}
  # webpack:
  #   webpackConfig: webpack.config.js
  #   includeModules: 
  #     forceInclude:
  #       - mysql2
  #   packager: "npm"
  esbuild:
    bundle: true
    minify: false  
  prune: ${self:custom.defaults.custom.prune}
  serverless-offline:
    httpPort: 4000
    stageVariables:
      foo: "bar"
  dlqName: ac-odh-event-ingestion-aflvfl-dlq-${self:provider.stage}
  AflVflQueueDLQ:
    name: ${file(../../defaults.yml):service}-dlq-${self:provider.stage}
    url: https://sqs.${self:provider.region}.amazonaws.com/${aws:accountId}/${self:custom.AflVflQueueDLQ.name}-error-queue.fifo

functions:
  parser: 
    name: ${self:custom.base}-parser
    handler: functions/parser.handler
    events:
      - msk:
          arn: ${self:custom.defaultParams.${self:provider.stage}.KAFKA_MSK_ARN}
          topic: ${self:custom.defaultParams.${self:provider.stage}.KAFKA_AFLVFL_INGESTION_TOPIC}
          startingPosition: LATEST
          batchSize: ${self:custom.defaultParams.${self:provider.stage}.KAFKA_CONSUMER_BATCH_SIZE}
    timeout: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.defaultParams.${self:provider.stage}.MEMORY_SIZE_LAMBDA_PARSER_AFLVFL}
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    logRetentionInDays: ${self:custom.defaultParams.${self:provider.stage}.LOG_RETENTION_IN_DAYS}
    layers:
        - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
        - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_281_2_20231117-044046_nodejs:1
    vpc:
      securityGroupIds:
          - ${cf:ac-odh-common-resources-sg-${self:provider.stage}.sgIdDigitalOdsRdsCredentials}
      subnetIds:
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_A}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_B}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_D}
    environment:
      LOG_STREAM_NAME: ${self:custom.defaultParams.${self:provider.stage}.LOG_STREAM_NAME}
      LAYER_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAYER_DEBUG_MODE}
      REGION: ${self:provider.region}
      PROCESS_IN_BATCHES_BATCH_SIZE: 1
      PROCESS_IN_BATCHES_RETRIES: 3
      DB_WRITER_LAMBDA_NAME: ${self:functions.dbWriter.name}
      PARSER_DLQ_URL: ${self:custom.AflVflQueueDLQ.url}
      DATABASE_NAME: ${self:custom.defaultParams.${self:provider.stage}.DATABASE_NAME}
      PARSER_DLQ_BATCH_SIZE: 1
      AWSREGION: ${aws:region}
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: ${self:custom.defaultParams.${self:provider.stage}.SECRET_DIGITAL_ODS_CREDENTIALS_DB}
      QUERY_MANAGER_DEBUG: ${self:custom.defaultParams.${self:provider.stage}.QUERY_MANAGER_DEBUG}
      DEFAULT_LANG: ${self:custom.defaultParams.${self:provider.stage}.DEFAULT_LANG}
      DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.DEBUG_MODE}
      LAYER_ENABLED: ${self:custom.defaultParams.${self:provider.stage}.LAYER_ENABLED}
      EXTENSION_LOG_MAX_ITEMS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_ITEMS}
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}
      EXTENSION_LOG_MAX_BYTES: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_BYTES}
      EXTENSION_LOG_TIMEOUT_MS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_TIMEOUT_MS}
      EXTENSION_LOG_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DEBUG_MODE}
      EXTENSION_LOG_ENVIRONMENT: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_ENVIRONMENT}
      EXTENSION_LOG_S3_BUCKET_REGION: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_S3_BUCKET_REGION}
      LAMBDA_LOG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_LOG_MODE}
      FAILED_EVENT_S3_BUCKET_NAME: ${cf:ac-odh-common-resources-s3-${self:provider.stage}.FailedEventBucketName}

  dbWriter:
    name: ${self:custom.base}-db-writer
    handler: functions/writer.handler
    timeout: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.defaultParams.${self:provider.stage}.MEMORY_SIZE_LAMBDA_WRITER_AFLVFL}
    logRetentionInDays: ${self:custom.defaultParams.${self:provider.stage}.LOG_RETENTION_IN_DAYS}
    layers:
        - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
        - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_281_2_20231117-044046_nodejs:1
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    vpc:
      securityGroupIds:
        - ${cf:ac-odh-common-resources-sg-${self:provider.stage}.sgIdDigitalOdsRdsCredentials}
      subnetIds:
        - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_A}
        - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_B}
        - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_D}
    environment:
      REGION: ${self:provider.region}
      PROCESS_IN_BATCHES_BATCH_SIZE: 10
      PROCESS_IN_BATCHES_RETRIES: 3
      DB_WRITER_LAMBDA_NAME: ${self:functions.dbWriter.handler}
      PARSER_DLQ_URL: ${self:custom.AflVflQueueDLQ.url}
      DATABASE_NAME: ${self:custom.defaultParams.${self:provider.stage}.DATABASE_NAME}
      PARSER_DLQ_BATCH_SIZE: 1
      AWSREGION: ${aws:region}
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: ${self:custom.defaultParams.${self:provider.stage}.SECRET_DIGITAL_ODS_CREDENTIALS_DB}
      QUERY_MANAGER_DEBUG: ${self:custom.defaultParams.${self:provider.stage}.QUERY_MANAGER_DEBUG}
      
      DEFAULT_LANG: ${self:custom.defaultParams.${self:provider.stage}.DEFAULT_LANG}
      DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.DEBUG_MODE}
      LAYER_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAYER_DEBUG_MODE}
      LAYER_ENABLED: ${self:custom.defaultParams.${self:provider.stage}.LAYER_ENABLED}
      EXTENSION_LOG_MAX_ITEMS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_ITEMS}
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}
      EXTENSION_LOG_MAX_BYTES: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_BYTES}
      EXTENSION_LOG_TIMEOUT_MS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_TIMEOUT_MS}
      EXTENSION_LOG_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DEBUG_MODE}
      EXTENSION_LOG_ENVIRONMENT: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_ENVIRONMENT}
      EXTENSION_LOG_S3_BUCKET_REGION: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_S3_BUCKET_REGION}
      LOG_STREAM_NAME: ${self:custom.defaultParams.${self:provider.stage}.LOG_STREAM_NAME}
      LAMBDA_LOG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_LOG_MODE}

  retry: 
    name: ${self:custom.base}-retry
    handler: functions/retry.handler
    events:
      - sqs:
          arn: ${cf:${self:custom.dlqName}.QueueARN}
          batchSize: 1
    timeout: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.defaultParams.${self:provider.stage}.MEMORY_SIZE_LAMBDA_PARSER_AFLVFL}
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ingestionLambdaRoleArn}
    logRetentionInDays: ${self:custom.defaultParams.${self:provider.stage}.LOG_RETENTION_IN_DAYS}
    layers:
        - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
        - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_281_2_20231117-044046_nodejs:1
    vpc:
      securityGroupIds:
          - ${cf:ac-odh-common-resources-sg-${self:provider.stage}.sgIdDigitalOdsRdsCredentials}
      subnetIds:
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_A}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_B}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_D}
    environment:
      LOG_STREAM_NAME: ${self:custom.defaultParams.${self:provider.stage}.LOG_STREAM_NAME}
      LAYER_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAYER_DEBUG_MODE}
      REGION: ${self:provider.region}
      PROCESS_IN_BATCHES_BATCH_SIZE: 1
      PROCESS_IN_BATCHES_RETRIES: 3
      DB_WRITER_LAMBDA_NAME: ${self:functions.dbWriter.name}
      PARSER_DLQ_URL: ${self:custom.AflVflQueueDLQ.url}
      DATABASE_NAME: ${self:custom.defaultParams.${self:provider.stage}.DATABASE_NAME}
      PARSER_DLQ_BATCH_SIZE: 1
      AWSREGION: ${aws:region}
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: ${self:custom.defaultParams.${self:provider.stage}.SECRET_DIGITAL_ODS_CREDENTIALS_DB}
      QUERY_MANAGER_DEBUG: ${self:custom.defaultParams.${self:provider.stage}.QUERY_MANAGER_DEBUG}
      DEFAULT_LANG: ${self:custom.defaultParams.${self:provider.stage}.DEFAULT_LANG}
      DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.DEBUG_MODE}
      LAYER_ENABLED: ${self:custom.defaultParams.${self:provider.stage}.LAYER_ENABLED}
      EXTENSION_LOG_MAX_ITEMS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_ITEMS}
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}
      EXTENSION_LOG_MAX_BYTES: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_BYTES}
      EXTENSION_LOG_TIMEOUT_MS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_TIMEOUT_MS}
      EXTENSION_LOG_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DEBUG_MODE}
      EXTENSION_LOG_ENVIRONMENT: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_ENVIRONMENT}
      EXTENSION_LOG_S3_BUCKET_REGION: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_S3_BUCKET_REGION}
      LAMBDA_LOG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_LOG_MODE}
      FAILED_EVENT_S3_BUCKET_NAME: ${cf:ac-odh-common-resources-s3-${self:provider.stage}.FailedEventBucketName}
      RETRY_ALERT_SNS_TOPIC_ARN: ${cf:${self:custom.dlqName}.SNSTopicARN}
  
  replayTrigger: 
    name: ${self:custom.base}-replay-trigger
    handler: functions/replay-trigger.handler
    timeout: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.defaultParams.${self:provider.stage}.MEMORY_SIZE_LAMBDA_PARSER_AFLVFL}
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ApiIngestionJobCommonRoleArn}
    logRetentionInDays: ${self:custom.defaultParams.${self:provider.stage}.LOG_RETENTION_IN_DAYS}
    layers:
        - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
        - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_281_2_20231117-044046_nodejs:1
    vpc:
      securityGroupIds:
          - ${cf:ac-odh-common-resources-sg-${self:provider.stage}.sgIdDigitalOdsRdsCredentials}
      subnetIds:
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_A}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_B}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_D}
    environment:
      LOG_STREAM_NAME: ${self:custom.defaultParams.${self:provider.stage}.LOG_STREAM_NAME}
      LAYER_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAYER_DEBUG_MODE}
      REGION: ${self:provider.region}
      PROCESS_IN_BATCHES_BATCH_SIZE: 1
      PROCESS_IN_BATCHES_RETRIES: 3
      DB_WRITER_LAMBDA_NAME: ${self:functions.dbWriter.name}
      PARSER_DLQ_URL: ${self:custom.AflVflQueueDLQ.url}
      DATABASE_NAME: ${self:custom.defaultParams.${self:provider.stage}.DATABASE_NAME}
      PARSER_DLQ_BATCH_SIZE: 1
      AWSREGION: ${aws:region}
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: ${self:custom.defaultParams.${self:provider.stage}.SECRET_DIGITAL_ODS_CREDENTIALS_DB}
      QUERY_MANAGER_DEBUG: ${self:custom.defaultParams.${self:provider.stage}.QUERY_MANAGER_DEBUG}
      DEFAULT_LANG: ${self:custom.defaultParams.${self:provider.stage}.DEFAULT_LANG}
      DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.DEBUG_MODE}
      LAYER_ENABLED: ${self:custom.defaultParams.${self:provider.stage}.LAYER_ENABLED}
      EXTENSION_LOG_MAX_ITEMS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_ITEMS}
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}
      EXTENSION_LOG_MAX_BYTES: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_BYTES}
      EXTENSION_LOG_TIMEOUT_MS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_TIMEOUT_MS}
      EXTENSION_LOG_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DEBUG_MODE}
      EXTENSION_LOG_ENVIRONMENT: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_ENVIRONMENT}
      EXTENSION_LOG_S3_BUCKET_REGION: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_S3_BUCKET_REGION}
      LAMBDA_LOG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_LOG_MODE}
      FAILED_EVENT_S3_BUCKET_NAME: ${cf:ac-odh-common-resources-s3-${self:provider.stage}.FailedEventBucketName}
      RETRY_ALERT_SNS_TOPIC_ARN: ${cf:${self:custom.dlqName}.SNSTopicARN}
      EVENT_STREAM_STORE_BUCKET: ${self:custom.defaultParams.${self:provider.stage}.EVENT_STREAM_STORE_BUCKET}
      EVENT_STREAM_STORE_PATH: ${self:custom.defaultParams.${self:provider.stage}.EVENT_STREAM_STORE_PATH}
      REPLAY_QUEUE: { Ref: ReplayQueueAflVfl }

  replay: 
    name: ${self:custom.base}-replay
    handler: functions/replay.handler
    events:
      - sqs:
          arn: 
            Fn::GetAtt: [ReplayQueueAflVfl, Arn]
          batchSize: 10
    timeout: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_TIMEOUT}
    memorySize: ${self:custom.defaultParams.${self:provider.stage}.MEMORY_SIZE_LAMBDA_PARSER_AFLVFL}
    role: ${cf:ac-odh-common-resources-roles-${self:provider.stage}.ApiIngestionJobCommonRoleArn}
    logRetentionInDays: ${self:custom.defaultParams.${self:provider.stage}.LOG_RETENTION_IN_DAYS}
    layers:
        - ${cf:${self:custom.layerStackName}.SRELoggingAPIExtensionLayerLambdaLayerQualifiedArn}
        - arn:aws:lambda:${self:provider.region}:725887861453:layer:Dynatrace_OneAgent_1_281_2_20231117-044046_nodejs:1
    vpc:
      securityGroupIds:
          - ${cf:ac-odh-common-resources-sg-${self:provider.stage}.sgIdDigitalOdsRdsCredentials}
      subnetIds:
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_A}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_B}
          - ${self:custom.defaultParams.${self:provider.stage}.RDS_CONNECTION_SUBNET_ID_D}
    environment:
      LOG_STREAM_NAME: ${self:custom.defaultParams.${self:provider.stage}.LOG_STREAM_NAME}
      LAYER_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAYER_DEBUG_MODE}
      REGION: ${self:provider.region}
      PROCESS_IN_BATCHES_BATCH_SIZE: 1
      PROCESS_IN_BATCHES_RETRIES: 3
      DB_WRITER_LAMBDA_NAME: ${self:functions.dbWriter.name}
      PARSER_DLQ_URL: ${self:custom.AflVflQueueDLQ.url}
      DATABASE_NAME: ${self:custom.defaultParams.${self:provider.stage}.DATABASE_NAME}
      PARSER_DLQ_BATCH_SIZE: 1
      AWSREGION: ${aws:region}
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: ${self:custom.defaultParams.${self:provider.stage}.SECRET_DIGITAL_ODS_CREDENTIALS_DB}
      QUERY_MANAGER_DEBUG: ${self:custom.defaultParams.${self:provider.stage}.QUERY_MANAGER_DEBUG}
      DEFAULT_LANG: ${self:custom.defaultParams.${self:provider.stage}.DEFAULT_LANG}
      DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.DEBUG_MODE}
      LAYER_ENABLED: ${self:custom.defaultParams.${self:provider.stage}.LAYER_ENABLED}
      EXTENSION_LOG_MAX_ITEMS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_ITEMS}
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME}
      EXTENSION_LOG_MAX_BYTES: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_MAX_BYTES}
      EXTENSION_LOG_TIMEOUT_MS: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_TIMEOUT_MS}
      EXTENSION_LOG_DEBUG_MODE: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_DEBUG_MODE}
      EXTENSION_LOG_ENVIRONMENT: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_ENVIRONMENT}
      EXTENSION_LOG_S3_BUCKET_REGION: ${self:custom.defaultParams.${self:provider.stage}.EXTENSION_LOG_S3_BUCKET_REGION}
      LAMBDA_LOG_MODE: ${self:custom.defaultParams.${self:provider.stage}.LAMBDA_LOG_MODE}
      FAILED_EVENT_S3_BUCKET_NAME: ${cf:ac-odh-common-resources-s3-${self:provider.stage}.FailedEventBucketName}
      RETRY_ALERT_SNS_TOPIC_ARN: ${cf:${self:custom.dlqName}.SNSTopicARN}

  
resources:
  - ${file(./resources/sqs.yml)}