const { LOGGER } = require('../utils');
const { aflvflService } = require("../services");
const { routeUtil, flowUtil } = require("../utils");
const get = require("lodash.get");

module.exports.handler = async (event, context) => {
    try {
        flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_PARSER)
        LOGGER.start(event, "Started aflvfl retry event");

        const rawRecords = await aflvflService.extractRecordsFromSqsEvent(event);
        LOGGER.info("Extracted records", rawRecords);
        if (rawRecords.length === 0) {
            return routeUtil.getSuccessResponse({ message: 'No AFL/VFL records' })
        }
        const decodedSqsRecords = aflvflService.decodeRawSqsRecord(rawRecords)
        const {
            recordsInsert,
            flightLegNotfoundRecords
        } = await aflvflService.formatData(decodedSqsRecords)

        const batchResponse = await aflvflService.invokeWriter(recordsInsert);

        const listBatchResponseFailed = get(batchResponse, "failures", []);
        const listBatchResponse = get(batchResponse, "responses", []);

        if (listBatchResponse.length > 0) {
            LOGGER.logServiceResponse(
                "response send message to writer function success",
                batchResponse.responses
            );
        }
        if (listBatchResponseFailed.length > 0 || flightLegNotfoundRecords.length > 0) {
            await aflvflService.handleRetryFailedEvents(event);
        }

        return routeUtil.getSuccessResponse({ message: 'Handler success' })
    } catch (err) {
        LOGGER.logServiceError("Error processing AFLVFL ingestion event", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, err);
        try {
            await aflvflService.handleRetryFailedEvents(event);
        } catch (dlqError) {
            LOGGER.logServiceError("Dlq message sending failed", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, dlqError);
        }
        return routeUtil.getErrorResponse(err)
    } finally {
        try {
            await LOGGER.sendLogsToKinesis();
        } catch (loggerErr) {
            LOGGER.error(loggerErr, `Dbaas logger sendLogsToKinesis failed!`);
        }
    }
};