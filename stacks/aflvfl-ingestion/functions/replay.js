const { flowUtil, LOGGER } = require("../utils");
const { aflvflService } = require("../services");

module.exports.handler = async (event, context) => {
    try {
        flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_PARSER);
        LOGGER.start(event, "Started aflvfl replay event");
        const rawRecords = await aflvflService.extractRecordsFromSqsEvent(event);
        LOGGER.info("Extracted records", rawRecords);
        if (rawRecords.length === 0) {
            return 'No AFL/VFL records to replay'
        }
        const extractedSqsRecords = aflvflService.decodeReplaySqsRecord(rawRecords)
        LOGGER.info("Extracted sqs records", extractedSqsRecords);
        const decodedSqsRecords = await aflvflService.getS3FileContent(extractedSqsRecords)
        LOGGER.info("Decoded sqs records", decodedSqsRecords);
        try {
            const {
                recordsInsert
            } = await aflvflService.formatData(decodedSqsRecords);
            LOGGER.info("Formatted records", recordsInsert);

            await aflvflService.writeReplayBatchData(recordsInsert);
        } catch (error) {
            LOGGER.logServiceError("Error processing & inserting AFLVFL replay event", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, error);
            await aflvflService.handleReplyFailedEvents(decodedSqsRecords)
            throw error
        }
        return 'Handler success'
    } catch (err) {
        LOGGER.logServiceError("Error processing AFLVFL replay event", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, err);
        throw err
    } finally {
        try {
            await LOGGER.sendLogsToKinesis();
        } catch (loggerErr) {
            LOGGER.error(loggerErr, `Dbaas logger sendLogsToKinesis failed!`);
        }
    }
};
