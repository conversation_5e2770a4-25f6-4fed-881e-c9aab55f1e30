const { LOGGER } = require('../utils');
const { aflvflService } = require("../services");
const { routeUtil, flowUtil } = require("../utils");

module.exports.handler = async (event, context) => {
  try {
    flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_PARSER)
    LOGGER.start(event, "ODH Event Ingestion AFLVFL Writer Lambda Function Started Event:");

    const response = await aflvflService.writeSingleEventData(event);

    return routeUtil.getSuccessResponse(response)
  } catch (err) {
    LOGGER.logServiceError("response event writer database error", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, err);
    return routeUtil.getErrorResponse(err)
  } finally {
    try {
      await LOGGER.sendLogsToKinesis();
    } catch (loggerErr) {
      LOGGER.error(loggerErr, `Dbaas logger sendLogsToKinesis failed!`);
    }
  }
};
