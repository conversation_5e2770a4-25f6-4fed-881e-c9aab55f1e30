const { aflvflService } = require("../services");
const { routeUtil, flowUtil, LOGGER } = require("../utils");
const get = require("lodash.get");

module.exports.handler = async (event, context) => {
    try {
        flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_PARSER);
        LOGGER.start(event, "Started aflvfl parser event ");
        const rawRecords = await aflvflService.extractRecordsFromKafkaEvent(event);
        LOGGER.info("Extracted records", rawRecords);
        if (rawRecords.length === 0) {
            return routeUtil.getSuccessResponse({ message: 'No AFL/VFL records' })
        }
        const decodedKafkaEvents = aflvflService.decodeRawKafkaRecord(rawRecords)
        LOGGER.info("Decoded records", decodedKafkaEvents);
        const {
            recordsInsert,
            originalRecords,
            flightLegNotfoundRecords
        } = await aflvflService.formatData(decodedKafkaEvents);
        LOGGER.info('Formatted records', recordsInsert)
        const batchResponse = await aflvflService.invokeWriter(recordsInsert);
        LOGGER.info('Batch invoke response', batchResponse)
        const listBatchResponseFailed = get(batchResponse, "failures", []);
        const listBatchResponse = get(batchResponse, "responses", []);

        if (listBatchResponse.length > 0) {
            LOGGER.logServiceResponse(
                "response send message to writer function success",
                batchResponse.responses
            );
        }
        await aflvflService.handleFailedParserEvents(flightLegNotfoundRecords, listBatchResponseFailed, originalRecords);
        return routeUtil.getSuccessResponse({ message: 'Handler success' })
    } catch (err) {
        LOGGER.logServiceError("Error processing AFLVFL ingestion event", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, err);
        try {
            const rawRecords = await aflvflService.extractRecordsFromKafkaEvent(event);
            const decodedKafkaEvents = aflvflService.decodeRawKafkaRecord(rawRecords);
            const failedRecords = decodedKafkaEvents.map(record => ({
                error: err,
                item: record.decodedAFLVFLRecord
            }));
            await aflvflService.sendErrorRecordsToQueue(failedRecords);
        } catch (dlqError) {
            LOGGER.logServiceError("Dlq message sending failed", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, dlqError);
        }
        return routeUtil.getErrorResponse(err)
    } finally {
        try {
            await LOGGER.sendLogsToKinesis();
        } catch (loggerErr) {
            LOGGER.error(loggerErr, `Dbaas logger sendLogsToKinesis failed!`);
        }
    }
};
