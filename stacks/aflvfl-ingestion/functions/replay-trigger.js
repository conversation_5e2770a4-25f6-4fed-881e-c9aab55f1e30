const { flowUtil, LOGGER } = require("../utils");
const { aflvflService } = require("../services");

module.exports.handler = async (event, context) => {
    try {
        flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME_PARSER);
        LOGGER.start(event, "Started aflvfl replay trigger event");

        aflvflService.validateInputParams(event.startDate, event.endDate);

        const response = await aflvflService.processReplayMessages(event.startDate, event.endDate)
        return response
    } catch (err) {
        LOGGER.logServiceError("Error processing AFLVFL ingestion event", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, err);
        throw err
    } finally {
        try {
            await LOGGER.sendLogsToKinesis();
        } catch (loggerErr) {
            LOGGER.error(loggerErr, `Dbaas logger sendLogsToKinesis failed!`);
        }
    }
};
