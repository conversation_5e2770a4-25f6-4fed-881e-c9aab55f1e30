const LambdaClient = require("aws-sdk/clients/lambda");

const lambda = new LambdaClient();

const invoke = async ({
  functionName,
  invocationType = "RequestResponse",
  payload,
  logType = "Tail",
}) => {
  try {
    const params = {
      FunctionName: functionName,
      InvocationType: invocationType,
      LogType: logType,
      Payload: JSON.stringify(payload),
    };
    const response = await lambda.invoke(params).promise();
    return response;
  } catch (err) {
    console.log('Lambda Invoke Error ',err);
    throw err;
  }
};

module.exports = {
  invoke,
};
