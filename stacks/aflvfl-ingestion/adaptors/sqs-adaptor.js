const SqsClient = require("aws-sdk/clients/sqs");
const uuid = require("uuid");

const sqs = new SqsClient();

const sendMessageToFifo = async ({ message, queueUrl, groupId = 'CertificateErrorDlq' }) => {
    const params = {
        MessageBody: JSON.stringify(message),
        QueueUrl: queueUrl,
        MessageGroupId: groupId,
        MessageDeduplicationId: uuid.v4()
    };
    const response = await sqs.sendMessage(params).promise();
    return response;
};

const sendBatchMessageToFifo = async ({ messages, queueUrl, groupId = 'CertificateErrorDlq' }) => {
    const entries = messages.map(message => ({
        Id: uuid.v4(),
        MessageBody: JSON.stringify({ error: message?.error?.stack, event: message?.item }),
        MessageGroupId: groupId,
        MessageDeduplicationId: uuid.v4(),
    }))
    const params = {
        Entries: entries,
        QueueUrl: queueUrl,
    };
    const response = await sqs.sendMessageBatch(params).promise();
    return response;
};

const sendBatchMessage = async ({ messages, queueUrl}) => {
    const entries = messages.map(message => ({
        Id: uuid.v4(),
        MessageBody: JSON.stringify(message)
    }))
    const params = {
        Entries: entries,
        QueueUrl: queueUrl,
    };
    const response = await sqs.sendMessageBatch(params).promise();
    return response;
};

module.exports = {
    sendMessageToFifo,
    sendBatchMessageToFifo,
    sendBatchMessage
};