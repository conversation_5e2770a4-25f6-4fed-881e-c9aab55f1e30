const { QueryManager, secretsManagerCache } = require("ac-utils");

const secrets = new secretsManagerCache();

async function connectWriterDatabase(secreteName, databaseName) {
  const queryManagerIns = new QueryManager();
  const auroraSecrets = await secrets.getSecret(secreteName);
  const auroraSecret = JSON.parse(auroraSecrets);
  queryManagerIns.initOnce({
    host: auroraSecret.hostProxyWR,
    user: auroraSecret.username,
    password: auroraSecret.password,
    database: databaseName,
    debug: process.env.QueryManagerDebug === "true",
  });
  return queryManagerIns;
}

async function connectReaderDatabase(secreteName, databaseName) {
  const queryManagerIns = new QueryManager();
  const auroraSecrets = await secrets.getSecret(secreteName);
  const auroraSecret = JSON.parse(auroraSecrets);
  queryManagerIns.initOnce({
    host: auroraSecret.hostProxyRD,
    user: auroraSecret.username,
    password: auroraSecret.password,
    database: databaseName,
    debug: process.env.QueryManagerDebug === "true",
  });
  return queryManagerIns;
}

module.exports = {
  connectWriterDatabase,
  connectReaderDatabase
}