{"name": "ac-odh-event-ingestion-aflvfl-writer", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest"}, "author": "", "license": "ISC", "dependencies": {"ac-utils": "git+ssh://**************/AC-IT-Development/ac-utils.git#1.4.0", "dbaas-logger": "git+ssh://**************/AC-IT-Development/dbaas-logger.git#vbeta_4.0.8", "lodash.get": "^4.4.2", "aws-sdk": "^2.958.0", "dotenv": "^8.2.0", "mysql2": "^2.3.3", "uuid": "^8.3.2"}, "devDependencies": {"copy-webpack-plugin": "^11.0.0", "better-sqlite3": "^7.6.2", "mysql": "^2.18.1", "mysql2": "^2.3.3", "oracledb": "^5.4.0", "pg": "^8.7.3", "pg-native": "^3.0.0", "pg-query-stream": "^4.2.3", "sqlite3": "^5.0.11", "tedious": "^15.0.1", "esbuild": "^0.14.43", "eslint": "^8.34.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "jest": "^29.0.3", "jest-sonar-reporter": "^2.0.0", "mock-aws-s3": "^4.0.2", "serverless": "^3.34.0", "serverless-esbuild": "^1.32.8", "serverless-offline": "^9.0.0", "serverless-plugin-log-retention": "^2.0.0", "serverless-prune-plugin": "^2.0.1", "serverless-webpack": "^5.7.1", "webpack": "^5.74.0", "webpack-node-externals": "^3.0.0"}, "jest": {"collectCoverage": true, "coveragePathIgnorePatterns": ["/node_modules/", "/test/", "/coverage/"], "moduleDirectories": ["node_modules"], "testResultsProcessor": "jest-sonar-reporter"}, "jestSonar": {"reportPath": "coverage", "reportFile": "reporter.xml", "indent": 4}, "directories": {"test": "test"}, "description": ""}