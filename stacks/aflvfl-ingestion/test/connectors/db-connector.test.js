const { dbConnector } = require("../../connectors");
const mysqlAdaptor = require('../../adaptors/mysql-adaptor');
const fs = require("fs");

const TEST_DATA = JSON.parse(
    fs.readFileSync(__dirname + "/test-data.json")
);

const extractedData = TEST_DATA.extractedData

const mockWhereRawResponse = jest.fn();
const mockWhereResponse = jest.fn(() => ({
    whereRaw: mockWhereRawResponse
}))
jest.mock(
    "../../adaptors/mysql-adaptor",
    () => ({
        connectReaderDatabase: jest.fn(
            () => ({
                getBuilder: jest.fn(
                    () => ({
                        table: jest.fn(() => ({
                            insert: jest.fn(() => ({
                                onConflict: jest.fn(() => (
                                    {
                                        merge: jest.fn(() => (
                                            { where: jest.fn() }
                                        ))
                                    }
                                )),
                            })),
                            select: jest.fn(() => ({
                                orderBy: jest.fn(() => ({
                                    limit: jest.fn(() => {
                                        const lastTransction = [{
                                            Id: 1,
                                        }];
                                        return lastTransction;
                                    }),
                                })),
                                where: mockWhereResponse
                            })),
                            where: jest.fn(() => ({
                                del: jest.fn(() => ({
                                })),
                            })),
                        }))
                    })
                )
            })
        )
    })
)

describe("ODH Db connector Unit Tests", () => {
    test("Test getCorrectIATACodeForZXFlights Function Success", async () => {
        mockWhereRawResponse.mockResolvedValue([
            {
                "ScheduledAt": "2024-01-16T00:00:00.000Z",
                "ActualDepartureAt": "2024-01-16T03:58:00.000Z"
            }
        ]);
        const response = await dbConnector.getCorrectIATACodeForZXFlights(extractedData);
        return expect(response).toEqual('AC')
    });

    test("Test getFlightLeg Function Success", async () => {
        mockWhereResponse.mockResolvedValue({});
        const response = await dbConnector.getFlightLeg(extractedData);
        return expect(response).toMatchObject({});
    });

});