const { s3Adaptor } = require("../../adaptors");
const { fileStorageConnector } = require('../../connectors');

beforeAll(() => {
    jest.useFakeTimers('modern');
    jest.setSystemTime(new Date("2023-01-01"));
});

beforeAll(() => {
    jest.spyOn(s3Adaptor, "putObject").mockImplementation(async () => {
        return new Promise((res) => {
            res({});
        })
    })
});

describe("ODH Db connector Unit Tests", () => {
    const FAILED_EVENT_S3_BUCKET_NAME = 'test-bucket'
    test("Test getFlightLeg Function Success", async () => {
        process.env.FAILED_EVENT_S3_BUCKET_NAME = FAILED_EVENT_S3_BUCKET_NAME
        const response = await fileStorageConnector.putRetryFunctionFailedEvents({});
        return expect(response).toMatchObject(
            {
                Bucket: FAILED_EVENT_S3_BUCKET_NAME,
                Key: 'aflvfl/year=2023/month=01/day=01/aflvfl-1672531200000.json',
                Body: '{}'
            }
        )
    });

});