const { createDbObj, extractAflVflFields } = require("../../services/data-mapping-service")
const fs = require("fs");

const TEST_DATA = JSON.parse(
    fs.readFileSync(__dirname + "/test-data.json")
);

const correctExtractFieldFunctionInput = TEST_DATA.correctExtractFieldFunctionInput
const correctExtractFieldFunctionOutput = TEST_DATA.correctExtractFieldFunctionOutput
const correctFormattedRecord = TEST_DATA.correctFormattedRecord
const correctDbObject = TEST_DATA.correctDbObject
const correctDbObjectInputTimestamp = TEST_DATA.correctDbObjectInputTimestamp

describe("ODH AFLVFL Data Mapping Service Unit Tests", () => {
    test("Test Extract aflvfl function", () => {
        const response = extractAflVflFields(correctExtractFieldFunctionInput, new Date("2024-01-16"));
        return expect(response).toMatchObject(correctExtractFieldFunctionOutput);
    });

    test("Test create db object function", () => {
        const response = createDbObj(correctFormattedRecord, correctDbObjectInputTimestamp);
        return expect(response).toMatchObject(correctDbObject);
    });
});