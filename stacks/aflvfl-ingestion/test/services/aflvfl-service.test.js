const { formatData, handleFailedParserEvents, writeSingleEventData } = require("../../services/aflvfl-service")
const { dbConnector, queueConnector } = require("../../connectors")
const fs = require("fs");

const TEST_DATA = JSON.parse(
    fs.readFileSync(__dirname + "/test-data.json")
);
const decodedEvent = TEST_DATA.decodedEvent;
const correctDbObject = TEST_DATA.correctDbObject;
const sampleFailureResponse = TEST_DATA.sampleFailureResponse;
const sampleCorrectOriginalEvent = TEST_DATA.sampleCorrectOriginalEvent;
const errorMessages = TEST_DATA.errorMessages;

beforeAll(() => {
    jest.spyOn(dbConnector, "getCorrectIATACodeForZXFlights").mockImplementation(async () => {
        return new Promise((res) => {
            res({
                iataCode: "AC",
                actualDepartureDate: "2024-01-16"
            });
        })
    })
    jest.spyOn(dbConnector, "getTransactionScope").mockImplementation(async () => {
        return new Promise((res) => {
            res({
                commit: jest.fn(
                    () =>
                        new Promise((res) => {
                            res({});
                        })
                )
            });
        })
    })
    jest.spyOn(dbConnector, "handleQueryInsert").mockImplementation(async () => {
        return new Promise((res) => {
            res({});
        })
    })
    jest.spyOn(dbConnector, "handleQueryUpsert").mockImplementation(async () => {
        return new Promise((res) => {
            res({});
        })
    })
    jest.spyOn(queueConnector, "sendBatchMessageToFifo").mockImplementation(async () => {
        return new Promise((res) => {
            res({});
        })
    })
});

describe("ODH AFLVFL Service Unit Tests", () => {
    test("Test format aflvfl response length", async () => {
        const response = await formatData(decodedEvent);
        return expect(response.recordsInsert.length).toBeGreaterThan(0);
    });

    test("Test format aflvfl record function", async () => {
        const response = await formatData(decodedEvent);
        return expect(response.recordsInsert[0]).toMatchObject(correctDbObject);
    });

    test("Test handle failed aflvfl event", async () => {
        const response = await handleFailedParserEvents([], sampleFailureResponse.failures, sampleCorrectOriginalEvent);
        return expect(response).toMatchObject(errorMessages);
    });

    test("Test handle aflvfl event", async () => {
        const response = await writeSingleEventData(correctDbObject);
        return expect(response).toMatchObject({});
    });
});