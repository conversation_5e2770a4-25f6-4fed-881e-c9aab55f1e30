const lambdaWriter = require("../../functions/writer");
const { aflvflService } = require("../../services/index")

jest.mock("../../utils", () => {
  const utils = jest.requireActual("../../utils/");
  return {
    ...utils,
    LOGGER: {
      ...utils.LOGGER,
      sendLogsToKinesis: jest.fn(
        () =>
          new Promise((res) => {
            res({});
          })
      )
    }
  }
});

beforeAll(() => {
  jest.spyOn(aflvflService, "writeSingleEventData").mockImplementation(async () => {
    return new Promise((res) => {
      res({});
    })
  })
});


describe("ODH AFLVFL Db Writer Lambda Unit Tests", () => {
  test("Test Handle Writer Lambda Function Success", async () => {
    const contextDataMock = {
      awsRequestId: 123456,
    };
    const response = await lambdaWriter.handler({}, contextDataMock);
    return expect(response.statusCode).toBe(200);
  });

});