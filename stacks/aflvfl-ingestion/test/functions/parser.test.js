const lambdaParser = require("../../functions/parser");
const { aflvflService } = require("../../services/index")
const fs = require("fs");

const TEST_DATA = JSON.parse(
  fs.readFileSync(__dirname + "/test-data.json")
);

const correctEvent = TEST_DATA.sampleCorrectEvent
const correctFormattedEvent = TEST_DATA.sampleCorrectFormattedEvent
const correctFormattedOriginalEvent = TEST_DATA.sampleCorrectOriginalEvent
const failureResponse = TEST_DATA.sampleFailureResponse

jest.mock("../../utils", () => {
  const utils = jest.requireActual("../../utils/");
  return {
    ...utils,
    LOGGER: {
      ...utils.LOGGER,
      sendLogsToKinesis: jest.fn(
        () =>
          new Promise((res) => {
            res({});
          })
      )
    }
  }
});

beforeAll(() => {
  jest.spyOn(aflvflService, "formatData").mockImplementation(async () => {
    return new Promise((res) => {
      res({
        recordsInsert: correctFormattedEvent,
        originalRecords: correctFormattedOriginalEvent,
        flightLegNotfoundRecords: []
      });
    })
  });

  jest.spyOn(aflvflService, "invokeWriter").mockImplementation(async () => {
    return new Promise((res) => {
      res({
        responses: [null],
        failures: []
      });
    })
  })
});


describe("ODH AFLVFL Event Unit Tests", () => {
  test("Test Handle Parser Lambda Function Success", async () => {
    const contextDataMock = {
      awsRequestId: 123456,
    };
    const response = await lambdaParser.handler(correctEvent, contextDataMock);
    return expect(response.statusCode).toBe(200);
  });

  // test("Test Handle Parser Lambda Function failure", async () => {
  //   jest.spyOn(aflvflService, "invokeWriter").mockImplementation(async () => {
  //     return new Promise((res) => {
  //       res(failureResponse);
  //     })
  //   })
  //   const contextDataMock = {
  //     awsRequestId: 123456,
  //   };
  //   const response = await lambdaParser.handler(correctEvent, contextDataMock);
  //   return expect(response.statusCode).toBe(500);
  // }, 15000);
}, 50000);