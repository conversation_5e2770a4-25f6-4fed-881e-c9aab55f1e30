Resources:
  ReplayQueueAflVfl:
    Type: AWS::SQS::Queue
    Properties:
      QueueName : ${self:custom.base}-replay-queue
      VisibilityTimeout: 900
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [ReplayQueuedDeadLetterQueueAflVfl, Arn]
        maxReceiveCount: 1
    
  ReplayQueuedDeadLetterQueueAflVfl:
      Type: AWS::SQS::Queue
      Properties:
        QueueName : ${self:custom.base}-replay-dlq