const serverlessWebpack = require("serverless-webpack");
const nodeExternals = require("webpack-node-externals");
const path = require("path");
module.exports = {
  target: "node",
  entry: serverlessWebpack.lib.entries,
  mode: serverlessWebpack.lib.webpack.isLocal ? "development" : "production",
  node: true,
  optimization: {
    minimize: true,
  },
  devtool: "inline-cheap-module-source-map",
  resolve: {
    alias: {
      common: path.resolve(__dirname, "../../common"),
    },
  },
  externalsPresets: { node: true },
  externals: [nodeExternals()],
};
