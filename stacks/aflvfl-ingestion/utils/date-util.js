const getScheduledDepartureDate = (currentDate, day) => {
    // normaly aflvfl event's scheduled date is current date
    const currentDay = currentDate.getUTCDate();
    let currentMonth = currentDate.getUTCMonth() + 1;
    let currentYear = currentDate.getUTCFullYear();

    if (currentDay != day) { // check whether event date is not equal to schedule at date and create moth and year
        if (currentDay < 7 && day > 21) { // this is a past date. So we need to get past month
            currentMonth = currentMonth - 1;
        }
        if (day < 7 && currentDay > 21) { // this is a future date. So we need to get next month
            currentMonth = currentMonth + 1;
        }
    }
    if (currentMonth == 0) {
        currentMonth = 12;
        currentYear = currentYear - 1;
    }
    if (currentMonth == 13) {
        currentMonth = 1;
        currentYear = currentYear + 1;
    }

    return currentYear + '-' + currentMonth.toString().padStart(2, '0') + '-' + day.toString().padStart(2, '0');
};

const getTimeFromString = (timeStamp) => {
    if (timeStamp) {
        const hour = timeStamp.substring(0, 2)
        const minutes = timeStamp.substring(2, 4)

        return hour + ":" + minutes + ":" + "00";
    } else {
        return null;
    }
}

const isValidDate = (date) => {
    return (date.toString() !== "Invalid Date");
}

const getAllDatesAndHoursBetweenRange = (startDateTime, endDateTime) => {
    const startDate = new Date(startDateTime);
    const endDate = new Date(endDateTime);
    const dateTimes = [];
    let currentDate = startDate

    while (currentDate <= endDate) {
        const year = currentDate.getFullYear();
        const month = `${currentDate.getMonth() + 1}`.padStart(2, '0');
        const day = `${currentDate.getDate()}`.padStart(2, '0');
        const hour = `${currentDate.getHours()}`.padStart(2, '0');
        dateTimes.push({
            year,
            month,
            day,
            hour
        });
        currentDate.setHours(currentDate.getHours() + 1);
    }
    return dateTimes;
}

module.exports = {
    getScheduledDepartureDate,
    getTimeFromString,
    isValidDate,
    getAllDatesAndHoursBetweenRange
}