const LAMBDA_EXIT_STATUS_SUCCESS = "Success";
const LAMBDA_EXIT_STATUS_VALID_ERROR = "Valid-Error";
const LAMBDA_EXIT_STATUS_EXCEPTION = "Exception";

const SERVICE_TYPE = "ODH event ingestion AFLVFL";
const SERVICE_NAME = "ac-odh-event-ingestion-aflvfl";

const SERVICE_NAME_PARSER= "ac-odh-event-ingestion-aflvfl-parser";
const SERVICE_NAME_WRITER = "ac-odh-event-ingestion-aflvfl-writer";
const SERVICE_NAME_RETRY = "ac-odh-event-ingestion-aflvfl-retry";

module.exports = {
  LAMBDA_EXIT_STATUS_SUCCESS,
  LAMBDA_EXIT_STATUS_VALID_ERROR,
  LAMBDA_EXIT_STATUS_EXCEPTION,
  SERVICE_TYPE,
  SERVICE_NAME,
  SERVICE_NAME_PARSER,
  SERVICE_NAME_WRITER,
  SERVICE_NAME_RETRY
}