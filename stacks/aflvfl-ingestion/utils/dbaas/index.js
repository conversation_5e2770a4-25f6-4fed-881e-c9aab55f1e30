const dbaas = require("dbaas-logger");
const GLOBAL_CONTEXT = require('../global');
const constants = require("./constants");

dbaas.dbaasLoggerConfig.streamName = process.env.LOG_STREAM_NAME;
dbaas.dbaasLoggerConfig.debugMode = process.env.DEBUG_MODE;


const SERVICE_TYPE = constants.SERVICE_TYPE;

const getServiceName = ()=>{
    return GLOBAL_CONTEXT.getFromContext('SERVICE_NAME') || constants.SERVICE_NAME
}

const start = (event, message) => {
    dbaas.start(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), `${message}: ${JSON.stringify(event)}`);
    dbaas.logMessage(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), "@@ Start Time : ", new Date().toISOString());
    dbaas.logMessage(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), "Event : ", event);
    const uniqueReqId = dbaas.logServiceRequest(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, JSON.stringify(event), "STRING");
    GLOBAL_CONTEXT.setInContext('uniqueReqId', uniqueReqId)
};

const getLogUniqueReqId = (event, message) => {
    return dbaas.logServiceRequest(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, JSON.stringify(event), "STRING");
};

const info = (message, event = {}) => {
    return dbaas.logMessage(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, event);
};

const success = (output, message) => {
    //TODO according to the doc, the logServiceResponse need to be used when lambda call external service
    dbaas.logServiceResponse(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, JSON.stringify(output), "STRING", GLOBAL_CONTEXT.globalContext.uniqueReqId);
    //TODO it seems the time is already there in all the logs, so no need to log it again
    dbaas.logMessage(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), "@@ End Time : ", new Date().toISOString());
    //TODO it seems no need to JSON.stringify the output, as it accept JSON object as response
    dbaas.logCompletion(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, constants.LAMBDA_EXIT_STATUS_SUCCESS, JSON.stringify(output));
};

const error = (err, message) => {
    dbaas.logServiceError(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, constants.LAMBDA_EXIT_STATUS_EXCEPTION, err, GLOBAL_CONTEXT.globalContext.uniqueReqId);
    dbaas.logMessage(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), "@@ End Time : ", new Date().toISOString());
    //TODO it is better to test manually, using Error object as the response for the log completion, since it accept only string,JSON,XML
    dbaas.logCompletion(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, constants.LAMBDA_EXIT_STATUS_EXCEPTION, err);
};

const validError = (res, message) => {
    dbaas.logCompletion(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, constants.LAMBDA_EXIT_STATUS_VALID_ERROR, res);
};

const logServiceRequest = (message, data) => {
    return dbaas.logServiceRequest(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, data, 'JSON');
};

const logServiceResponse = (message, res) => {
    dbaas.logServiceResponse(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, res, 'JSON', GLOBAL_CONTEXT.globalContext.uniqueReqId);
}

const logServiceError = (message, errorCode, err) => {
    dbaas.logServiceError(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, errorCode, err, GLOBAL_CONTEXT.globalContext.uniqueReqId);
}


const logMessage = (message, data) => {
    return dbaas.logMessage(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, data);
};

const logError = (message, data) => {
    return dbaas.logMessage(GLOBAL_CONTEXT.globalContext, SERVICE_TYPE, getServiceName(), message, data, dbaas.dbaasLogLevels.error);
};

const sendLogsToKinesis = () => {
    return dbaas.sendLogsToKinesis(GLOBAL_CONTEXT.globalContext);
};

module.exports = {
    start,
    getLogUniqueReqId,
    success,
    error,
    validError,
    info,
    dbaas,
    logServiceRequest,
    logServiceResponse,
    logServiceError,
    logMessage,
    logError,
    constants,
    sendLogsToKinesis
}
