const getSuccessResponse = (res) => {
    const response = {
        status: "success",
        data: res
    }
    return {
        statusCode: 200,
        body: JSON.stringify(response)
    };
};

const getErrorResponse = (error) => {
    const response = {
        status: "error",
        error: error.message
    }
    return {
        statusCode: 500,
        body: JSON.stringify(response)
    };
};

const decodeEvent = (event) => {
    let requestContextClaims = event.requestContext && event.requestContext.authorizer;

    if(requestContextClaims && Object.keys(requestContextClaims).includes('claims')){
        requestContextClaims = requestContextClaims.claims;
    }

    if (requestContextClaims && requestContextClaims.data && typeof requestContextClaims.data === "string") {
        requestContextClaims.data = JSON.parse(requestContextClaims.data);
    }

    return {
        path: event.headers.path,
        payload: event.body && event.body.length > 0 ? JSON.parse(event.body) : {},
        headers: event.headers,
        claims: requestContextClaims
    };
};

module.exports = {
    getErrorResponse,
    getSuccessResponse,
    decodeEvent
};
