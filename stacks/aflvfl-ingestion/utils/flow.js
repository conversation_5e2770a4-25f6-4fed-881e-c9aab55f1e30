const GLOBAL_CONTEXT = require("./global");

const initiateGenericFlow = (event, context, serviceName) => {
    const headers = event.headers || {};
    GLOBAL_CONTEXT.setInContext('authorization', headers.Authorization);
    GLOBAL_CONTEXT.setInContext("headers", { "x-amzn-trace-id": context.awsRequestId });
    GLOBAL_CONTEXT.setInContext("ctx", context);
    GLOBAL_CONTEXT.setInContext("SERVICE_NAME", serviceName);
}

module.exports = {
    initiateGenericFlow,
};
