const { sqsAdaptor } = require('../adaptors');
const { commonUtil } = require('../utils');
const { PARSER_DLQ_URL, REPLAY_QUEUE } = process.env;

const sendFifoMessage = (message) => {
    return sqsAdaptor.sendMessageToFifo({ message, queueUrl: PARSER_DLQ_URL })
}

const sendBatchMessageToFifo = (messages) => {
    return sqsAdaptor.sendBatchMessageToFifo({ messages, queueUrl: PARSER_DLQ_URL })
}

const sendReplayEventBatchMessage = async (messages) => {
    const chunkedArray = commonUtil.chunkArray(messages, 10)
    const promises = chunkedArray.map(message => {
        return sqsAdaptor.sendBatchMessage({ messages: message, queueUrl: REPLAY_QUEUE })
    })
    await Promise.all(promises)
}

module.exports = {
    sendFifoMessage,
    sendBatchMessageToFifo,
    sendReplayEventBatchMessage
}