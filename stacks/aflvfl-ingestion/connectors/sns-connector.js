const { snsAdaptor } = require("../adaptors");
const { RETRY_ALERT_SNS_TOPIC_ARN } = process.env;

const publishRetryFunctionFailedEvents = async (fileContent) => {
    const params = {
        Message: JSON.stringify(fileContent),
        Subject: 'AFLVFL Ingestion Event Processing Fail In Retry',
        TopicArn: RETRY_ALERT_SNS_TOPIC_ARN
    };
    await snsAdaptor.publishMessage(params)
};

module.exports = {
    publishRetryFunctionFailedEvents,
};
