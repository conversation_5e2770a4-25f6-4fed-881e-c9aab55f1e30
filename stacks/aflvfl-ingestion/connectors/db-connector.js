const { mysqlAdaptor } = require('../adaptors');
const { dateUtil} = require('../utils');

const DB_SECRETS = process.env.SECRET_DIGITAL_ODS_CREDENTIALS_DB;
const DB_NAME = process.env.DATABASE_NAME;

const getCorrectIATACodeForZXFlights = async (
    { iataCode, flightNumber, scheduleDepartureDate, departureAirportCode },
    isZXcarrierCode
) => {
    const query = await mysqlAdaptor.connectReaderDatabase(DB_SECRETS, DB_NAME);
    const builder = query.getBuilder();

    const recievedIataCode = iataCode;
    if (isZXcarrierCode) {
        iataCode = "RV";
    }

    const result = await builder
        .table("FlightLeg")
        .select("ScheduledAt", "ActualDepartureAt")
        .where({
            CarrierCode: iataCode,
            Number: flightNumber,
            Suffix: '',
            DepartureAirportCode: departureAirportCode
        }).whereRaw('DATE(LocalScheduledAt) = ?', [scheduleDepartureDate]);
        console.log("Result ", JSON.stringify(result));
    if (result && result.length === 1) {
        return iataCode;
    } else if (isZXcarrierCode) {
        const resultSecondLookup = await builder
            .table("FlightLeg")
            .select("CarrierCode", "ScheduledAt", "ActualDepartureAt")
            .where({
                Number: flightNumber,
                DepartureAirportCode: departureAirportCode,
                Suffix: ''
            }).whereRaw('DATE(LocalScheduledAt) = ?', [scheduleDepartureDate]);
        console.log("Result Second Lookup ", JSON.stringify(resultSecondLookup));
        if (resultSecondLookup && resultSecondLookup.length === 1) {
            iataCode = resultSecondLookup[0].CarrierCode;
            console.log('IATA code ', iataCode)
            return iataCode;
        } else {
            throw new Error(`No records found for recieved event data 
            carrier code ${iataCode}, 
            number ${flightNumber}, 
            scheduledAt ${scheduleDepartureDate},
            departureAirportCode ${departureAirportCode}
            originalIataCode ${recievedIataCode}`)
        }
    } else {
        throw new Error(`No records found for recieved event data 
            carrier code ${iataCode}, 
            number ${flightNumber}, 
            scheduledAt ${scheduleDepartureDate},
            departureAirportCode ${departureAirportCode}`)
    }
};

const getActualDepartureDateFromResult = (result) => {
    let actualDepartureDate;
    if (result[0].ActualDepartureAt && dateUtil.isValidDate(result[0].ActualDepartureAt)) {
        const actualDepartureAt = result[0].ActualDepartureAt;
        console.log("actualDepartureAt ", actualDepartureAt);
        const actualDepartureDateObj = new Date(actualDepartureAt)
        actualDepartureDate = actualDepartureDateObj.toISOString().split("T")[0];
    } else {
        const scheduledAt = result[0].ScheduledAt;
        actualDepartureDate = scheduledAt.toISOString().split("T")[0];
        console.log("ActualDepartureAt is null in FlightLegTable, ScheduledAt is used");
    }
    console.log("actualDepartureAt ", actualDepartureDate);
    return actualDepartureDate;
};


const getFlightLeg = async ({
    iataCode,
    flightNumber,
    scheduleDepartureDate,
    departureAirportCode,
}) => {
    const query = await mysqlAdaptor.connectReaderDatabase(DB_SECRETS, DB_NAME);
    const builder = query.getBuilder();

    const result = await builder
        .table('FlightLeg')
        .select('ScheduledAt', 'ActualDepartureAt')
        .where({
            CarrierCode: iataCode,
            Number: flightNumber,
            Suffix: '',
            DepartureAirportCode: departureAirportCode,
            ScheduledAt: scheduleDepartureDate,
        });
    return result
};

const getTransactionScope = async () => {
    const queryManager = await mysqlAdaptor.connectWriterDatabase(DB_SECRETS, DB_NAME);
    const trx = await queryManager.transaction();
    return trx
}

const handleQueryUpsert = async (trx, table, data) => {
    const listFieldUpdate = JSON.parse(JSON.stringify(data))
    listFieldUpdate['RecordModifiedAt'] = new Date();
    const statement = trx
        .table(table)
        .insert(data)
        .onConflict()
        .merge(listFieldUpdate);
    console.log("process in statement: ", statement);
    return statement;
}

const handleQueryInsert = async (trx, table, data) => {
    await trx.table(table).insert(data);
}

module.exports = {
    getCorrectIATACodeForZXFlights,
    getFlightLeg,
    getTransactionScope,
    handleQueryUpsert,
    handleQueryInsert
};
