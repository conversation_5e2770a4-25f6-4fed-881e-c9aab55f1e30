const { s3Adaptor } = require("../adaptors");
const { LOGGER } = require("../utils");

const putRetryFunctionFailedEvents = async (fileContent) => {
    const date = new Date();
    const year = date.getFullYear();
    const month = `${date.getMonth() + 1}`.padStart(2, '0');
    const day = `${date.getDate()}`.padStart(2, '0');
    const timestamp = date.getTime();
    const folderPrefix = 'aflvfl/year=' + year + '/month=' + month + '/day=' + day + '/';
    const fileName = 'aflvfl-' + timestamp + '.json';
    const filenameKey = folderPrefix + fileName;

    const params = {
        Bucket: process.env.FAILED_EVENT_S3_BUCKET_NAME,
        Key: filenameKey,
        Body: JSON.stringify(fileContent)
    };
    await s3Adaptor.putObject(params)
    return params
};

const listObjectsForGivenDate = async (year, month, day, hour) => {
    const folderPrefix = `${process.env.EVENT_STREAM_STORE_PATH}year=${year}/month=${month}/day=${day}/hour=${hour}`;
    const params = {
        Bucket: process.env.EVENT_STREAM_STORE_BUCKET,
        Prefix: folderPrefix
    };
    LOGGER.info('params------>', params)
    const response = await s3Adaptor.listObjects(params)
    LOGGER.info('s3 response---->', response)
    if (response) {
        return {
            contents: response.Contents,
            keyCount: response.KeyCount,
            eventReceivedDate: `${year}-${month}-${day}`
        }
    }
    return response
};

const getRecordFromObject = async (bucket, key) => {
    const params = {
        Bucket: bucket,
        Key: key
    };
    LOGGER.info('params------>', params)
    const response = await s3Adaptor.getObject(params)
    LOGGER.info('s3 response---->', response)
    if (response.Body) {
        const content = response.Body.toString('utf-8');
        return JSON.parse(content)
    }
    return null
};

module.exports = {
    putRetryFunctionFailedEvents,
    listObjectsForGivenDate,
    getRecordFromObject
};
