const { dateUtil, constant } = require("../utils");

const extractAflVflFields = (record, eventReceivedDate) => {
    const transactionChode = record.substring(29, 32);
    const iataCode = record.substring(33, 35); 
    let flightNumber = record.substring(35, 39); 
    let sddDayLocal = record.substring(40, 42);
    let eventDayUTC = record.substring(43,45)
    const departureAirportCode = record.substring(46, 49);
    const timeOfEvent = record.substring(58, 62);
    const flightLevelRollDuration = record.substring(64, 67);
    let fuelOnBoard = record.substring(68, 72);
    let fuelBurn = record.substring(73, 77); 
    const eventIdentifier = record.substring(78, 79);
    const apuCycleText = record.substring(80, 83); 
    const apuElapsedText = record.substring(84, 88);

    flightNumber = Number(flightNumber).toString();
    fuelOnBoard = Number(fuelOnBoard) * 100;
    fuelBurn = Number(fuelBurn) * 100;
    sddDayLocal = Number(sddDayLocal);
    const flightLegEventTypeCode = _getFlightLegEventTypeCode(eventIdentifier, transactionChode);
    const scheduleDepartureDate = dateUtil.getScheduledDepartureDate(eventReceivedDate, sddDayLocal);
    const eventDateUTC = dateUtil.getScheduledDepartureDate(eventReceivedDate, eventDayUTC);
    let occurredAt = dateUtil.getTimeFromString(timeOfEvent);
    let flightLevel = 0; // setting default 0 since this is part of the primary key
    let rollTime = null;
    let apuCycle = null;
    let apuElapsed = null;

    if (flightLegEventTypeCode == constant.APPROACH || flightLegEventTypeCode == constant.ARRIVAL_FLIGHT_LEVEL || flightLegEventTypeCode == constant.VACATING_FLIGHT_LEVEL) {
        flightLevel = Number(flightLevelRollDuration) * 100;
    }
    if (flightLegEventTypeCode == constant.STARTTAKEOF) { 
        rollTime = Number(flightLevelRollDuration)
    }

    if (flightLegEventTypeCode == constant.LANDED && apuCycleText && apuElapsedText) {
        // if (apuCycleText.trim().length > 0) {
            apuCycle = Number(apuCycleText);
        // }
        // if (apuElapsedText.trim().length > 0) {
            apuElapsed = Number(apuElapsedText);
        // }
    }

    occurredAt = eventDateUTC + ' ' + occurredAt;
    
    const obj = {
        iataCode,
        flightNumber,
        scheduleDepartureDate,
        departureAirportCode,
        occurredAt,
        flightLevel,
        rollTime,
        fuelOnBoard,
        fuelBurn,
        flightLegEventTypeCode,
        apuCycle,
        apuElapsed,
    };
    return obj;
};

const createDbObj = (aFlVflRecord, timeStamp) => {
    

    const dbObj = {
        FlightLegFuelMeasurement: {
            FlightLegCarrierCode: aFlVflRecord.iataCode,
            FlightLegNumber: aFlVflRecord.flightNumber,
            FlightLegSuffix: "",
            FlightLegDepartureAirportCode: aFlVflRecord.departureAirportCode,
            FlightLegScheduledAt: aFlVflRecord.scheduleDepartureDate,
            OccurredAt: aFlVflRecord.occurredAt,
            FlightLevel: aFlVflRecord.flightLevel,
            RollDuration: aFlVflRecord.rollTime,
            FuelOnBoardWeight: aFlVflRecord.fuelOnBoard,
            FuelBurnedWeight: aFlVflRecord.fuelBurn,
            FlightEventTypeCode: aFlVflRecord.flightLegEventTypeCode,
            ApuCycles: aFlVflRecord.apuCycle,
            ApuDuration: aFlVflRecord.apuElapsed,
            RecordVersion: timeStamp
        },
        Transaction: {
            Schema: '2.0',
            TransactionTypeCode: 'AFLVFL',
            RecordVersion: timeStamp
        },
        FlightLegFuelMesurementTransaction: {
            FlightLegCarrierCode: aFlVflRecord.iataCode,
            FlightLegNumber: aFlVflRecord.flightNumber,
            FlightLegSuffix: "",
            FlightLegDepartureAirportCode: aFlVflRecord.departureAirportCode,
            FlightLegScheduledAt: aFlVflRecord.scheduleDepartureDate,
            FlightLegFuelMeasurementOccurredAt: aFlVflRecord.occurredAt,
            FlightLegFuelMeasurementFlightLevel: aFlVflRecord.flightLevel,
            RecordVersion: timeStamp
        }
    };
    return dbObj;
};

const _getFlightLegEventTypeCode = (eventIdentifier, transactionChode) => {
    let evetTypeCode;
    if (transactionChode === 'AFL') {
        if (eventIdentifier === 'A') {
            evetTypeCode = constant.APPROACH;
        } else if (eventIdentifier === 'O') {
            evetTypeCode = constant.LANDED;
        } else if (eventIdentifier === 'I') {
            evetTypeCode = constant.ARRIVALGATE;
        } else if (!isNaN(eventIdentifier)) {
            evetTypeCode = constant.ARRIVAL_FLIGHT_LEVEL;
        }
    } else if (transactionChode === 'VFL') {
        if (!isNaN(eventIdentifier)) {
            evetTypeCode = constant.VACATING_FLIGHT_LEVEL;
        } else if (eventIdentifier === 'T') {
            evetTypeCode = constant.STARTTAKEOF;
        }
    }
    return evetTypeCode;
}

module.exports = {
    extractAflVflFields,
    createDbObj
}