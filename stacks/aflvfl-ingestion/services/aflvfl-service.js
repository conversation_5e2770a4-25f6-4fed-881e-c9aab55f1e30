const { commonUtil, LOGGER, dateUtil } = require("../utils");
const dataMappingService = require('./data-mapping-service');
const { lambdaConnector, queueConnector, fileStorageConnector, snsConnector, dbConnector } = require('../connectors');
const uuid = require('uuid');
const { partialFailures, retrier } = require("ac-utils");

const PROCESS_IN_BATCHES_RETRIES = process.env.PROCESS_IN_BATCHES_RETRIES || 3;

const extractRecordsFromKafkaEvent = (event) => {
    const eventRecords = event.records;
    const eventRawData = eventRecords[Object.keys(eventRecords)[0]];
    console.log("extractRecordsFromKafkaEvent eventRawData",eventRawData)
    return eventRawData ? eventRawData : []
}

const extractRecordsFromSqsEvent = (event) => {
    const eventRecords = event.Records;
    return eventRecords ? eventRecords : []
}

const decodeRawKafkaRecord = (rawRecords) => {
    return rawRecords.map(eventRawDataItem => ({
        timeStamp: eventRawDataItem.timestamp,
        decodedAFLVFLRecord: commonUtil.decodeBase64(eventRawDataItem.value)
    }))
}

const decodeRawSqsRecord = (rawRecords) => {
    return rawRecords.map(eventRawDataItem => ({
        timeStamp: eventRawDataItem.attributes.SentTimestamp,
        decodedAFLVFLRecord: JSON.parse(eventRawDataItem.body).event
    }))
}

const decodeReplaySqsRecord = (rawRecords) => {
    return rawRecords.map(eventRawDataItem => ({
        timeStamp: eventRawDataItem.attributes.SentTimestamp,
        body: JSON.parse(eventRawDataItem.body)
    }))
}

const formatData = async (records) => {
    const recordsInsert = [];
    const originalRecords = [];
    const flightLegNotfoundRecords = [];
    for (const record of records) {
        const uniqueMatchingId = uuid.v4();
        originalRecords.push({
            uniqueMatchingId,
            decodedAFLVFLRecord: record.decodedAFLVFLRecord
        })
        const { timeStamp, decodedAFLVFLRecord, eventReceivedDate } = record;
        const eventTriggeredDate = eventReceivedDate ? new Date(eventReceivedDate) : new Date()
        const processedAflValData = dataMappingService.extractAflVflFields(decodedAFLVFLRecord, eventTriggeredDate);
        const isZXcarrierCode = processedAflValData.iataCode === 'ZX' ? true : false;

        if(isZXcarrierCode){ 
            // ZX iata code is not there in ODH. most probably ZX refer as RV(according to Shreyans and AirCOM team)
            // Correct iata code will get from FlightLeg table
            const iataCode  = await dbConnector.getCorrectIATACodeForZXFlights(processedAflValData, isZXcarrierCode);
            if (iataCode) {
                processedAflValData['iataCode'] = iataCode;
            } 
        }

        
        const dbObj = dataMappingService.createDbObj(processedAflValData, timeStamp);
        dbObj['uniqueMatchingId'] = uniqueMatchingId;
        recordsInsert.push(dbObj)
    }
    return {
        recordsInsert,
        originalRecords,
        flightLegNotfoundRecords
    }
}

const invokeWriter = async (recordsInsert) => {
    const batchResponse = await partialFailures.processInBatches({
        list: recordsInsert,
        batchSize: process.env.PROCESS_IN_BATCHES_BATCH_SIZE || 10,
        callback: async (message) => _invokeWriterLambda(message),
        retries: PROCESS_IN_BATCHES_RETRIES,
    });
    return batchResponse
}

const handleFailedParserEvents = async (flightLegNotfoundRecords, listBatchResponseFailed, originalRecords) => {
    if (flightLegNotfoundRecords.length === 0 && listBatchResponseFailed.length === 0) {
        return
    }
    LOGGER.logServiceError("Error happen while inserting data", LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, listBatchResponseFailed);
    const errorMessage = []
    listBatchResponseFailed.map(response => {
        const originalRecord = originalRecords.find(record => record.uniqueMatchingId === response.item.uniqueMatchingId)
        if (originalRecord) {
            errorMessage.push({
                error: response.error,
                item: originalRecord.decodedAFLVFLRecord
            })
        }
    })
    flightLegNotfoundRecords.map(response => {
        const originalRecord = originalRecords.find(record => record.uniqueMatchingId === response.uniqueMatchingId)
        if (originalRecord) {
            errorMessage.push({
                error: 'No record found in FlightLeg table',
                item: originalRecord.decodedAFLVFLRecord
            })
        }
    })
    LOGGER.info("Error formatted messages", errorMessage);
    if (errorMessage.length > 0) {
        await sendErrorRecordsToQueue(errorMessage);
    }
    return errorMessage
}

const handleRetryFailedEvents = async (event) => {
    const fileContent = JSON.parse(event.Records[0].body);
    try {
        await fileStorageConnector.putRetryFunctionFailedEvents(fileContent.event)
        LOGGER.info('File uploaded to s3 successfully');
    } catch (error) {
        LOGGER.error(error, 'Error while uploading file to s3:');
    }
    try {
        await snsConnector.publishRetryFunctionFailedEvents(fileContent)
        LOGGER.info('Email sent successfully');
    } catch (error) {
        LOGGER.error(error, 'Error sending email:');
    }
}

const handleReplyFailedEvents = async (events) => {
    const fileContent = events.map(event => (event.decodedAFLVFLRecord))
    for (const record of fileContent) {
        try {
            await fileStorageConnector.putRetryFunctionFailedEvents(record)
            LOGGER.info('File uploaded to s3 successfully');
        } catch (error) {
            LOGGER.error(error, 'Error while uploading file to s3:');
        }
    }
}

const sendErrorRecordsToQueue = async (errorMessage) => {
    await queueConnector.sendBatchMessageToFifo(errorMessage)
    LOGGER.info('Send error message to queue', errorMessage);
    return
}

const writeAflVflData = async (event, trx) => {
    const transactionId = uuid.v4();
    const transactionObj = {
        ...event.Transaction,
        Id: transactionId
    }
    const flightLegFuelMeasurementObj = {
        ...event.FlightLegFuelMeasurement
    };
    const flightLegFuelMeasurementTransactionObj = {
        ...event.FlightLegFuelMesurementTransaction,
        TransactionId: transactionId
    };
    await dbConnector.handleQueryInsert(trx, 'Transaction', transactionObj)
    await dbConnector.handleQueryUpsert(trx, 'FlightLegFuelMeasurement', flightLegFuelMeasurementObj)
    await dbConnector.handleQueryUpsert(trx, 'FlightLegFuelMeasurementTransaction', flightLegFuelMeasurementTransactionObj)
}

const writeReplayBatchData = async (data) => {
    const trx = await dbConnector.getTransactionScope();
    try {
        for (const event of data) {
            await writeAflVflData(event, trx)
        }
        const dbResponse = await trx.commit();
        LOGGER.logServiceResponse('response from createRawTransactionData', dbResponse)
        return dbResponse
    } catch (error) {
        LOGGER.logServiceError('Error while executing createRawTransactionData', LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, error)
        await trx.rollback();
        throw error
    }
}

const writeSingleEventData = async (data) => {
    const trx = await dbConnector.getTransactionScope();
    try {
        await writeAflVflData(data, trx)
        const dbResponse = await trx.commit();
        LOGGER.logServiceResponse('response from createRawTransactionData', dbResponse)
        return dbResponse
    } catch (error) {
        LOGGER.logServiceError('Error while executing createRawTransactionData', LOGGER.constants.LAMBDA_EXIT_STATUS_EXCEPTION, error)
        await trx.rollback();
        throw error
    }
}

const validateInputParams = async (startDate, endDate) => {
    const dateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
    if (!dateTimeRegex.test(startDate) || !dateTimeRegex.test(endDate)) {
        throw new Error('Invalid input date')
    }
    return true
};

const processReplayMessages = async (startDate, endDate) => {
    const { contents, count } = await listAllObjectsForGivenDate(startDate, endDate);
    LOGGER.info('formatted result count', count);
    const messages = formatReplayQueueMessage(contents)
    LOGGER.info('formatted messages', messages);
    await queueConnector.sendReplayEventBatchMessage(messages)
    return `${count} messages send to queue`
}

const listAllObjectsForGivenDate = async (startDate, endDate) => {
    const dateHours = dateUtil.getAllDatesAndHoursBetweenRange(startDate, endDate);
    const chunkedArray = commonUtil.chunkArray(dateHours, 10)
    const contents = [];
    let count = 0
    for (const chunk of chunkedArray) {
        const { data, keyCount } = await parallelProcessSingleChunk(chunk)
        contents.push(...data)
        count = count + keyCount;
    }
    return { contents, count }
};

const formatReplayQueueMessage = (contents) => {
    return contents.map(content => ({
        ...content,
        bucket: process.env.EVENT_STREAM_STORE_BUCKET
    }))
}

const parallelProcessSingleChunk = async (chunk) => {
    const chunkPromises = chunk.map(iterator => {
        return fileStorageConnector.listObjectsForGivenDate(iterator.year, iterator.month, iterator.day, iterator.hour)
    })
    const results = await Promise.all(chunkPromises)
    const data = []
    let keyCount = 0
    results.map(result => {
        console.log('-------result------->', result)
        result.contents.map(content => {
            data.push({
                key: content.Key,
                eventReceivedDate: result.eventReceivedDate
            })
        })
        keyCount = keyCount + result.keyCount
    })
    return { data, keyCount }
}

const getS3FileContent = async (rawRecords) => {
    const output = []
    for (const record of rawRecords) {
        const timestamp = record.timeStamp
        const body = record.body
        const fileData = await fileStorageConnector.getRecordFromObject(body.bucket, body.key)
        LOGGER.info('Extracted file data', fileData)
        output.push({
            timeStamp: timestamp,
            decodedAFLVFLRecord: fileData,
            eventReceivedDate: body.eventReceivedDate
        })
    }
    return output
}

const _invokeWriterLambda = async (payload) => {
    return await retrier({
        callback: async () => {
            const dbWriterResponse = await lambdaConnector.invokeWriterLambda(payload);
            const dbWriterResponsePayload = JSON.parse(dbWriterResponse.Payload);
            if (dbWriterResponsePayload.statusCode !== 200) {
                const err = new Error(`DB Writer failed to process: ${dbWriterResponse.toString()}`);
                throw err;
            }
        },
        attempts: PROCESS_IN_BATCHES_RETRIES,
    });
};

module.exports = {
    extractRecordsFromKafkaEvent,
    extractRecordsFromSqsEvent,
    decodeRawKafkaRecord,
    decodeRawSqsRecord,
    decodeReplaySqsRecord,
    formatData,
    invokeWriter,
    handleFailedParserEvents,
    sendErrorRecordsToQueue,
    handleRetryFailedEvents,
    processReplayMessages,
    validateInputParams,
    getS3FileContent,
    writeReplayBatchData,
    writeSingleEventData,
    handleReplyFailedEvents
}