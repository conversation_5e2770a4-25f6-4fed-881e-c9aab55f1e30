service: ${file(../../defaults.yml):service}-dlq
variablesResolutionMode: 20210326

projectDir: ../../../

provider:
  name: aws
  region: ${opt:region, 'ca-central-1'}
  stage: ${opt:stage, "intca1"}     
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}
custom:
  base: ${self:service}-${self:provider.stage}
  defaults: ${file(../../defaults.yml)}
  defaultParams: ${self:custom.defaults.custom.params} 
  params:
    EMAIL_DLQ_1: ${self:custom.defaultParams.${self:provider.stage}.EMAIL_DLQ_1}  
    EMAIL_DLQ_2: ${self:custom.defaultParams.${self:provider.stage}.EMAIL_DLQ_2}  
  sns:
    arn: arn:aws:sns:${aws:region}:${aws:accountId}
    FilterTopic:
      name: ${self:custom.base}-filter-topic.fifo
      arn: ${self:custom.sns.arn}:${self:custom.sns.FilterTopic.name}
    AFLVFLErrorAlarmTopic:
        name: ${self:custom.base}-AFLVFLError-alarm-topic
        arn: ${self:custom.sns.arn}:${self:custom.sns.AFLVFLErrorAlarmTopic.name}
  sqs:
    params:
      MaximumMessageSize: 262144
      VisibilityTimeout: 310
      MessageRetentionPeriod: 345600
      ReceiveMessageWaitTimeSeconds: 0
      DelaySeconds: 60
    base:
      arn: arn:aws:sqs:${self:provider.region}:${aws:accountId}
    queues:
      AFLVFLErrorQueueDLQ:
        name: ${self:custom.base}-error-queue.fifo
        arn: ${self:custom.sqs.base.arn}:${self:custom.sqs.queues.AFLVFLErrorQueueDLQ.name}
        url: https://sqs.${self:provider.region}.amazonaws.com/${aws:accountId}/${self:custom.sqs.queues.AFLVFLErrorQueueDLQ.name}        
resources:            
  Resources: 
    AFLVFLErrorQueueDLQ: 
      Type: AWS::SQS::Queue
      Properties: 
        FifoQueue: true
        MaximumMessageSize: ${self:custom.sqs.params.MaximumMessageSize}
        MessageRetentionPeriod: ${self:custom.sqs.params.MessageRetentionPeriod}
        ReceiveMessageWaitTimeSeconds: ${self:custom.sqs.params.ReceiveMessageWaitTimeSeconds}
        VisibilityTimeout: ${self:custom.sqs.params.VisibilityTimeout}
        QueueName : ${self:custom.sqs.queues.AFLVFLErrorQueueDLQ.name}
        DelaySeconds: ${self:custom.sqs.params.DelaySeconds}        
    AFLVFLErrorAlarmTopic: 
      Type: AWS::SNS::Topic
      Properties: 
        DisplayName: ${self:custom.sns.AFLVFLErrorAlarmTopic.name}
        TopicName:  ${self:custom.sns.AFLVFLErrorAlarmTopic.name}        
        Subscription: 
          - Endpoint: ${self:custom.params.EMAIL_DLQ_1}
            Protocol: "email"
          - Endpoint: ${self:custom.params.EMAIL_DLQ_2}
            Protocol: "email"

  Outputs: 
    QueueURL: 
      Description: "URL of new Amazon SQS Queue"
      Value: 
        Ref: "AFLVFLErrorQueueDLQ"
    QueueARN: 
      Description: "ARN of new AmazonSQS Queue"
      Value: 
        Fn::GetAtt: 
          - "AFLVFLErrorQueueDLQ"
          - "Arn"
    QueueName: 
      Description: "Name of new Amazon SQS Queue"
      Value: 
        Fn::GetAtt: 
          - "AFLVFLErrorQueueDLQ" 
          - "QueueName"
    SNSTopicARN:
      Description: "ARN of SNS Topic For Alarm"
      Value:
        Ref: "AFLVFLErrorAlarmTopic"