service: ${self:custom.service}-aflvfl-glue-catalog

provider:
  name: aws
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  runtime: nodejs14.x
  stackTags: ${self:custom.tags}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  defaultParams: ${self:custom.defaults.custom.params}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}
 
resources:
  Resources:
    GlueDatabaseAflVfl: ${file(./resources/glueDatabaseAflVfl.yml)}
    GlueSourceTableAflVfl: ${file(./resources/glueSourceTableAflVfl.yml)}
