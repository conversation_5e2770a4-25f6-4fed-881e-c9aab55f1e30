DependsOn: GlueDatabaseAflVfl
Type: AWS::Glue::Table
Properties:
  CatalogId: !Ref AWS::AccountId
  DatabaseName: !Ref GlueDatabaseAflVfl
  TableInput:
    Name: s3_aflvfl_datastore_aircraft_telemetry_summary
    TableType: EXTERNAL_TABLE
    StorageDescriptor:
      Location: s3://${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET_NAME}/
      OutputFormat: org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat
      InputFormat: org.apache.hadoop.mapred.TextInputFormat
      Columns:
        - Name: transaction_code
          Type: string
        - Name: airline_id
          Type: string
        - Name: flight_number
          Type: string
        - Name: schedule_date_day
          Type: string
        - Name: utc_date_of_event_day
          Type: string
        - Name: departure_station
          Type: string
        - Name: destination_station
          Type: string
        - Name: aircraft_fin_number
          Type: string
        - Name: time_of_event_in_utc
          Type: string
        - Name: utc_identifier
          Type: string
        - Name: flight_level_roll_time
          Type: string
        - Name: total_fob
          Type: string
        - Name: fuel_burn
          Type: string
        - Name: identifier
          Type: string
      SerdeInfo:
        Parameters:
          input.format: '^(")?.{11}\s*.{15}\s*(?<transaction_code>.{5}).{1}(?<airline_id>.{2})(?<flight_number>.{4}).{1}(?<schedule_date_day>.{2}).{1}(?<utc_date_of_event_day>.{2}).{1}(?<departure_station>.{3}).{1}(?<destination_station>.{3}).{1}(?<aircraft_fin_number>.{3}).{1}(?<time_of_event_in_utc>.{4})(?<utc_identifier>.{1}).{1}(?<flight_level_roll_time>.{3}).{1}(?<total_fob>.{4}).{1}(?<fuel_burn>.{4}).{1}(?<identifier>.{1})'
        SerializationLibrary: com.amazonaws.glue.serde.GrokSerDe