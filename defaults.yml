service: ac-odh-event-ingestion-aflvfl
provider:
  region: ${opt:region, "ca-central-1"}
custom:
  tags:
    project: Air Canada Argo ODH
    department: Digital
    application: ODH Event Ingestion AFLVFL
    service: ac-odh-event-ingestion-aflvfl
    repository: https://bitbucket.org/aircanada-m3/ac-odh-event-ingestion-aflvfl
    environment: ${self:provider.stage}
  prune:
    automatic: true
    number: 1

  params:
    intca1:
      CONNECTOR_CONFIGURATION_BUCKET_NAME: ac-odh-event-streaming-msk-connect-aflvfl-plugin-intca1/aflvfl/RAW-AFLVFL-AIRCOM-INTCA1
      KAFKA_MSK_ARN: arn:aws:kafka:ca-central-1:166083508693:cluster/digital-msk-intca1/efd13acf-2227-45ec-a32d-43c225aadd51-3
      KAFKA_AFLVFL_INGESTION_TOPIC: RAW-AFLVFL-AIRCOM-INTCA1
      KAFKA_CONSUMER_BATCH_SIZE: 300
      LOG_STREAM_NAME: SLSInfo
      LAYER_DEBUG_MODE: true
      EMAIL_DLQ_1: <EMAIL>
      EMAIL_DLQ_2: <EMAIL>
      LOG_RETENTION_IN_DAYS: 7
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: intca1/digital-ods/db-credentials
      CW_PERMISSION: "Allow"
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-intca1-default
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: intca1
      QUERY_MANAGER_DEBUG: true
      DEFAULT_LANG: "en"
      DEBUG_MODE: true
      LAMBDA_LOG_MODE: debug
      LAMBDA_TIMEOUT: 300
      MEMORY_SIZE_LAMBDA_WRITER_AFLVFL: 256
      MEMORY_SIZE_LAMBDA_PARSER_AFLVFL: 256
      DATABASE_NAME: FlightScheduleDataStore

      RDS_CONNCET_VPC_ID: vpc-0b8bcf38152af77cc
      RDS_CONNECTION_SUBNET_ID_A: subnet-07612cb342a5be91d
      RDS_CONNECTION_SUBNET_ID_B: subnet-01644fc5d5a72b235
      RDS_CONNECTION_SUBNET_ID_D: subnet-0656557afe0f2bc5e
      EVENT_STREAM_STORE_BUCKET: ac-odh-stream-event-store-intca1
      EVENT_STREAM_STORE_PATH: aflvfl/RAW-AFLVFL-AIRCOM-INTCA1/
      
    batca1:
      CONNECTOR_CONFIGURATION_BUCKET_NAME: ac-odh-event-streaming-msk-connect-aflvfl-plugin-batca1/aflvfl/RAW-AFLVFL-AIRCOM-BATCA1
      KAFKA_MSK_ARN: arn:aws:kafka:ca-central-1:574529944728:cluster/digital-msk-batca1/6415732f-1c7d-4e62-aacf-95c089a0ef75-2
      KAFKA_AFLVFL_INGESTION_TOPIC: RAW-AFLVFL-AIRCOM-BATCA1
      KAFKA_CONSUMER_BATCH_SIZE: 300
      LOG_STREAM_NAME: SLSInfo
      LAYER_DEBUG_MODE: false
      EMAIL_DLQ_1: <EMAIL>
      EMAIL_DLQ_2: <EMAIL>
      LOG_RETENTION_IN_DAYS: 7
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: batca1/digital-ods/db-credentials
      CW_PERMISSION: "Deny"
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-batca1-default
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: batca1
      QUERY_MANAGER_DEBUG: true
      DEFAULT_LANG: "en"
      DEBUG_MODE: false
      LAMBDA_LOG_MODE: debug
      LAMBDA_TIMEOUT: 300
      MEMORY_SIZE_LAMBDA_WRITER_AFLVFL: 256
      MEMORY_SIZE_LAMBDA_PARSER_AFLVFL: 256
      DATABASE_NAME: FlightScheduleDataStore

      RDS_CONNCET_VPC_ID: vpc-0abed1f5dc9bec60b
      RDS_CONNECTION_SUBNET_ID_A: subnet-093ec232ccaaf9236
      RDS_CONNECTION_SUBNET_ID_B: subnet-0e040f56cf4be65bc
      RDS_CONNECTION_SUBNET_ID_D: subnet-0fd7784cf1d74d946
      EVENT_STREAM_STORE_BUCKET: ac-odh-stream-event-store-batca1
      EVENT_STREAM_STORE_PATH: aflvfl/RAW-AFLVFL-AIRCOM-BATCA1/

    crtca1:
      CONNECTOR_CONFIGURATION_BUCKET_NAME: ac-odh-event-streaming-msk-connect-aflvfl-plugin-crtca1/aflvfl/RAW-AFLVFL-AIRCOM-CRTCA1
      KAFKA_MSK_ARN: arn:aws:kafka:ca-central-1:822319740706:cluster/digital-msk-crtca1/8180f3b2-f49f-4bc9-b062-63c73cfb18ac-4
      KAFKA_AFLVFL_INGESTION_TOPIC: RAW-AFLVFL-AIRCOM-CRTCA1
      KAFKA_CONSUMER_BATCH_SIZE: 300
      LOG_STREAM_NAME: SLSInfo
      LAYER_DEBUG_MODE: false
      EMAIL_DLQ_1: <EMAIL>
      EMAIL_DLQ_2: <EMAIL>
      LOG_RETENTION_IN_DAYS: 7
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: crtca1/digital-ods/db-credentials
      CW_PERMISSION: "Deny"
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-crtca1-default
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: crtca1
      QUERY_MANAGER_DEBUG: true
      DEFAULT_LANG: "en"
      DEBUG_MODE: false
      LAMBDA_LOG_MODE: debug
      LAMBDA_TIMEOUT: 300
      MEMORY_SIZE_LAMBDA_WRITER_AFLVFL: 256
      MEMORY_SIZE_LAMBDA_PARSER_AFLVFL: 256
      DATABASE_NAME: FlightScheduleDataStore

      RDS_CONNCET_VPC_ID: vpc-05c9f9824109cd1e7
      RDS_CONNECTION_SUBNET_ID_A: subnet-0e030f7de3818424f
      RDS_CONNECTION_SUBNET_ID_B: subnet-0f064c984d71076c7
      RDS_CONNECTION_SUBNET_ID_D: subnet-0cfe753e530b47cc8
      EVENT_STREAM_STORE_BUCKET: ac-odh-stream-event-store-crtca1
      EVENT_STREAM_STORE_PATH: aflvfl/RAW-AFLVFL-AIRCOM-CRTCA1/

    preprodca1:
      CONNECTOR_CONFIGURATION_BUCKET_NAME: ac-odh-event-streaming-msk-connect-aflvfl-plugin-preprodca1/aflvfl/RAW-AFLVFL-AIRCOM-PREPRODCA1
      KAFKA_MSK_ARN: arn:aws:kafka:ca-central-1:308974006580:cluster/digital-msk-preprodca1/1a4dd74b-ff0e-4edb-8371-7a480a5ac461-3
      KAFKA_AFLVFL_INGESTION_TOPIC: RAW-AFLVFL-AIRCOM-PREPRODCA1
      KAFKA_CONSUMER_BATCH_SIZE: 300
      LOG_STREAM_NAME: SLSInfo
      LAYER_DEBUG_MODE: false
      EMAIL_DLQ_1: <EMAIL>
      EMAIL_DLQ_2: <EMAIL>
      LOG_RETENTION_IN_DAYS: 7
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: preprodca1/digital-ods/db-credentials
      CW_PERMISSION: "Allow"
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-preprodca1-default
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: preprodca1
      QUERY_MANAGER_DEBUG: false
      DEFAULT_LANG: "en"
      DEBUG_MODE: false
      LAMBDA_LOG_MODE: debug
      LAMBDA_TIMEOUT: 300
      MEMORY_SIZE_LAMBDA_WRITER_AFLVFL: 256
      MEMORY_SIZE_LAMBDA_PARSER_AFLVFL: 256
      DATABASE_NAME: FlightScheduleDataStore

      RDS_CONNCET_VPC_ID: vpc-0019f2acd966dc61c
      RDS_CONNECTION_SUBNET_ID_A: subnet-0ce0a9dbac4cfc597
      RDS_CONNECTION_SUBNET_ID_B: subnet-06dd18a1cfb259360
      RDS_CONNECTION_SUBNET_ID_D: subnet-0e01bb22ef83c9ca3
      EVENT_STREAM_STORE_BUCKET: ac-odh-stream-event-store-preprodca1
      EVENT_STREAM_STORE_PATH: aflvfl/RAW-AFLVFL-AIRCOM-PREPRODCA1/

    prodca1:
      CONNECTOR_CONFIGURATION_BUCKET_NAME: ac-odh-event-streaming-msk-connect-aflvfl-plugin-prodca1/aflvfl/RAW-AFLVFL-AIRCOM-PRODCA1
      KAFKA_MSK_ARN: arn:aws:kafka:ca-central-1:414944252970:cluster/digital-msk-prodca1/602a1803-fd95-4a17-b74b-7c6781c4d09c-4
      KAFKA_AFLVFL_INGESTION_TOPIC: RAW-AFLVFL-AIRCOM-PRODCA1
      KAFKA_CONSUMER_BATCH_SIZE: 300
      LOG_STREAM_NAME: SLSInfo
      LAYER_DEBUG_MODE: false
      EMAIL_DLQ_1: <EMAIL>
      EMAIL_DLQ_2: <EMAIL>
      LOG_RETENTION_IN_DAYS: 7
      SECRET_DIGITAL_ODS_CREDENTIALS_DB: prodca1/digital-ods/db-credentials
      CW_PERMISSION: "Allow"
      LAYER_ENABLED: true
      EXTENSION_LOG_MAX_ITEMS: 10000
      EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME: dbaas-sre-v3-logs-prodca1-default
      EXTENSION_LOG_S3_BUCKET_REGION: ca-central-1
      EXTENSION_LOG_MAX_BYTES: 1048576
      EXTENSION_LOG_TIMEOUT_MS: 25
      EXTENSION_LOG_DEBUG_MODE: false
      EXTENSION_LOG_ENVIRONMENT: prodca1
      QUERY_MANAGER_DEBUG: false
      DEFAULT_LANG: "en"
      DEBUG_MODE: false
      LAMBDA_LOG_MODE: debug
      LAMBDA_TIMEOUT: 300
      MEMORY_SIZE_LAMBDA_WRITER_AFLVFL: 256
      MEMORY_SIZE_LAMBDA_PARSER_AFLVFL: 256
      DATABASE_NAME: FlightScheduleDataStore

      RDS_CONNCET_VPC_ID: vpc-067e948bf49f6ec92
      RDS_CONNECTION_SUBNET_ID_A: subnet-0895b4b087142e29f
      RDS_CONNECTION_SUBNET_ID_B: subnet-0e21904f481f055d1
      RDS_CONNECTION_SUBNET_ID_D: subnet-075c6590cab8b8957
      EVENT_STREAM_STORE_BUCKET: ac-odh-stream-event-store-prodca1
      EVENT_STREAM_STORE_PATH: aflvfl/RAW-AFLVFL-AIRCOM-PRODCA1/